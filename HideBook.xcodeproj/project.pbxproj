// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		287E94462DE5C65A004EEDFB /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 287E94402DE5C65A004EEDFB /* Assets.xcassets */; };
		287E94482DE5C65A004EEDFB /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 287E94432DE5C65A004EEDFB /* LaunchScreen.storyboard */; };
		287E94732DE5D0E2004EEDFB /* LeatherModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94662DE5D0E2004EEDFB /* LeatherModel.swift */; };
		287E94742DE5D0E2004EEDFB /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94612DE5D0E2004EEDFB /* Constants.swift */; };
		287E94752DE5D0E2004EEDFB /* MainTabBarController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94682DE5D0E2004EEDFB /* MainTabBarController.swift */; };
		287E94762DE5D0E2004EEDFB /* StockDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E946A2DE5D0E2004EEDFB /* StockDetailViewController.swift */; };
		287E94772DE5D0E2004EEDFB /* AddLeatherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E945D2DE5D0E2004EEDFB /* AddLeatherViewController.swift */; };
		287E94782DE5D0E2004EEDFB /* AddUsageRecordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E945F2DE5D0E2004EEDFB /* AddUsageRecordViewController.swift */; };
		287E94792DE5D0E2004EEDFB /* LeatherDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94642DE5D0E2004EEDFB /* LeatherDataManager.swift */; };
		287E947A2DE5D0E2004EEDFB /* StockUsageViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E946E2DE5D0E2004EEDFB /* StockUsageViewController.swift */; };
		287E947B2DE5D0E2004EEDFB /* LeatherDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94652DE5D0E2004EEDFB /* LeatherDetailViewController.swift */; };
		287E947C2DE5D0E2004EEDFB /* UIView+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94702DE5D0E2004EEDFB /* UIView+Extensions.swift */; };
		287E947D2DE5D0E2004EEDFB /* StockTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E946D2DE5D0E2004EEDFB /* StockTableViewCell.swift */; };
		287E947E2DE5D0E2004EEDFB /* UsageTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94722DE5D0E2004EEDFB /* UsageTableViewCell.swift */; };
		287E947F2DE5D0E2004EEDFB /* UIColor+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E946F2DE5D0E2004EEDFB /* UIColor+Extensions.swift */; };
		287E94802DE5D0E2004EEDFB /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94602DE5D0E2004EEDFB /* AppDelegate.swift */; };
		287E94812DE5D0E2004EEDFB /* AddStockEntryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E945E2DE5D0E2004EEDFB /* AddStockEntryViewController.swift */; };
		287E94822DE5D0E2004EEDFB /* LeatherArchiveViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94632DE5D0E2004EEDFB /* LeatherArchiveViewController.swift */; };
		287E94832DE5D0E2004EEDFB /* LeatherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94672DE5D0E2004EEDFB /* LeatherTableViewCell.swift */; };
		287E94842DE5D0E2004EEDFB /* StockStatisticsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E946C2DE5D0E2004EEDFB /* StockStatisticsViewController.swift */; };
		287E94852DE5D0E2004EEDFB /* StockDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94692DE5D0E2004EEDFB /* StockDataManager.swift */; };
		287E94862DE5D0E2004EEDFB /* GradientView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94622DE5D0E2004EEDFB /* GradientView.swift */; };
		287E94872DE5D0E2004EEDFB /* UsageDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94712DE5D0E2004EEDFB /* UsageDetailViewController.swift */; };
		287E94882DE5D0E2004EEDFB /* StockModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E946B2DE5D0E2004EEDFB /* StockModel.swift */; };
		287E948A2DE5D4BE004EEDFB /* AnalyticsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 287E94892DE5D4BE004EEDFB /* AnalyticsViewController.swift */; };
		9A12ADEFEE488F2B088BBF2A /* Pods_HideBook.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6E6F17A7A9094E1CE72F4826 /* Pods_HideBook.framework */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		287E94272DE5C64C004EEDFB /* HideBook.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = HideBook.app; sourceTree = BUILT_PRODUCTS_DIR; };
		287E94402DE5C65A004EEDFB /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		287E94412DE5C65A004EEDFB /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		287E94422DE5C65A004EEDFB /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		287E945D2DE5D0E2004EEDFB /* AddLeatherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddLeatherViewController.swift; sourceTree = "<group>"; };
		287E945E2DE5D0E2004EEDFB /* AddStockEntryViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddStockEntryViewController.swift; sourceTree = "<group>"; };
		287E945F2DE5D0E2004EEDFB /* AddUsageRecordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddUsageRecordViewController.swift; sourceTree = "<group>"; };
		287E94602DE5D0E2004EEDFB /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		287E94612DE5D0E2004EEDFB /* Constants.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		287E94622DE5D0E2004EEDFB /* GradientView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GradientView.swift; sourceTree = "<group>"; };
		287E94632DE5D0E2004EEDFB /* LeatherArchiveViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeatherArchiveViewController.swift; sourceTree = "<group>"; };
		287E94642DE5D0E2004EEDFB /* LeatherDataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeatherDataManager.swift; sourceTree = "<group>"; };
		287E94652DE5D0E2004EEDFB /* LeatherDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeatherDetailViewController.swift; sourceTree = "<group>"; };
		287E94662DE5D0E2004EEDFB /* LeatherModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeatherModel.swift; sourceTree = "<group>"; };
		287E94672DE5D0E2004EEDFB /* LeatherTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LeatherTableViewCell.swift; sourceTree = "<group>"; };
		287E94682DE5D0E2004EEDFB /* MainTabBarController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabBarController.swift; sourceTree = "<group>"; };
		287E94692DE5D0E2004EEDFB /* StockDataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockDataManager.swift; sourceTree = "<group>"; };
		287E946A2DE5D0E2004EEDFB /* StockDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockDetailViewController.swift; sourceTree = "<group>"; };
		287E946B2DE5D0E2004EEDFB /* StockModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockModel.swift; sourceTree = "<group>"; };
		287E946C2DE5D0E2004EEDFB /* StockStatisticsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockStatisticsViewController.swift; sourceTree = "<group>"; };
		287E946D2DE5D0E2004EEDFB /* StockTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockTableViewCell.swift; sourceTree = "<group>"; };
		287E946E2DE5D0E2004EEDFB /* StockUsageViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StockUsageViewController.swift; sourceTree = "<group>"; };
		287E946F2DE5D0E2004EEDFB /* UIColor+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIColor+Extensions.swift"; sourceTree = "<group>"; };
		287E94702DE5D0E2004EEDFB /* UIView+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIView+Extensions.swift"; sourceTree = "<group>"; };
		287E94712DE5D0E2004EEDFB /* UsageDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UsageDetailViewController.swift; sourceTree = "<group>"; };
		287E94722DE5D0E2004EEDFB /* UsageTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UsageTableViewCell.swift; sourceTree = "<group>"; };
		287E94892DE5D4BE004EEDFB /* AnalyticsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AnalyticsViewController.swift; sourceTree = "<group>"; };
		30030F43461AEABBEDD0346C /* Pods-HideBook.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HideBook.release.xcconfig"; path = "Target Support Files/Pods-HideBook/Pods-HideBook.release.xcconfig"; sourceTree = "<group>"; };
		65AFF5DA8214CDE49FA72B8A /* Pods-HideBook.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-HideBook.debug.xcconfig"; path = "Target Support Files/Pods-HideBook/Pods-HideBook.debug.xcconfig"; sourceTree = "<group>"; };
		6E6F17A7A9094E1CE72F4826 /* Pods_HideBook.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_HideBook.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		287E94242DE5C64C004EEDFB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				9A12ADEFEE488F2B088BBF2A /* Pods_HideBook.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		287E941E2DE5C64C004EEDFB = {
			isa = PBXGroup;
			children = (
				287E94442DE5C65A004EEDFB /* HideBook */,
				287E94282DE5C64C004EEDFB /* Products */,
				8383A5F8152E6DDF81F136B3 /* Pods */,
				9AB4DB97E4763C6221D4949B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		287E94282DE5C64C004EEDFB /* Products */ = {
			isa = PBXGroup;
			children = (
				287E94272DE5C64C004EEDFB /* HideBook.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		287E94442DE5C65A004EEDFB /* HideBook */ = {
			isa = PBXGroup;
			children = (
				287E94892DE5D4BE004EEDFB /* AnalyticsViewController.swift */,
				287E945D2DE5D0E2004EEDFB /* AddLeatherViewController.swift */,
				287E945E2DE5D0E2004EEDFB /* AddStockEntryViewController.swift */,
				287E945F2DE5D0E2004EEDFB /* AddUsageRecordViewController.swift */,
				287E94602DE5D0E2004EEDFB /* AppDelegate.swift */,
				287E94612DE5D0E2004EEDFB /* Constants.swift */,
				287E94622DE5D0E2004EEDFB /* GradientView.swift */,
				287E94632DE5D0E2004EEDFB /* LeatherArchiveViewController.swift */,
				287E94642DE5D0E2004EEDFB /* LeatherDataManager.swift */,
				287E94652DE5D0E2004EEDFB /* LeatherDetailViewController.swift */,
				287E94662DE5D0E2004EEDFB /* LeatherModel.swift */,
				287E94672DE5D0E2004EEDFB /* LeatherTableViewCell.swift */,
				287E94682DE5D0E2004EEDFB /* MainTabBarController.swift */,
				287E94692DE5D0E2004EEDFB /* StockDataManager.swift */,
				287E946A2DE5D0E2004EEDFB /* StockDetailViewController.swift */,
				287E946B2DE5D0E2004EEDFB /* StockModel.swift */,
				287E946C2DE5D0E2004EEDFB /* StockStatisticsViewController.swift */,
				287E946D2DE5D0E2004EEDFB /* StockTableViewCell.swift */,
				287E946E2DE5D0E2004EEDFB /* StockUsageViewController.swift */,
				287E946F2DE5D0E2004EEDFB /* UIColor+Extensions.swift */,
				287E94702DE5D0E2004EEDFB /* UIView+Extensions.swift */,
				287E94712DE5D0E2004EEDFB /* UsageDetailViewController.swift */,
				287E94722DE5D0E2004EEDFB /* UsageTableViewCell.swift */,
				287E94402DE5C65A004EEDFB /* Assets.xcassets */,
				287E94412DE5C65A004EEDFB /* Info.plist */,
				287E94432DE5C65A004EEDFB /* LaunchScreen.storyboard */,
			);
			path = HideBook;
			sourceTree = "<group>";
		};
		8383A5F8152E6DDF81F136B3 /* Pods */ = {
			isa = PBXGroup;
			children = (
				65AFF5DA8214CDE49FA72B8A /* Pods-HideBook.debug.xcconfig */,
				30030F43461AEABBEDD0346C /* Pods-HideBook.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		9AB4DB97E4763C6221D4949B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				6E6F17A7A9094E1CE72F4826 /* Pods_HideBook.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		287E94262DE5C64C004EEDFB /* HideBook */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 287E943A2DE5C64D004EEDFB /* Build configuration list for PBXNativeTarget "HideBook" */;
			buildPhases = (
				947356162A22AF74B66678F5 /* [CP] Check Pods Manifest.lock */,
				287E94232DE5C64C004EEDFB /* Sources */,
				287E94242DE5C64C004EEDFB /* Frameworks */,
				287E94252DE5C64C004EEDFB /* Resources */,
				DEB3BF2DC944B30EB3FB4051 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = HideBook;
			productName = HideBook;
			productReference = 287E94272DE5C64C004EEDFB /* HideBook.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		287E941F2DE5C64C004EEDFB /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					287E94262DE5C64C004EEDFB = {
						CreatedOnToolsVersion = 16.2;
						LastSwiftMigration = 1620;
					};
				};
			};
			buildConfigurationList = 287E94222DE5C64C004EEDFB /* Build configuration list for PBXProject "HideBook" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 287E941E2DE5C64C004EEDFB;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 287E94282DE5C64C004EEDFB /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				287E94262DE5C64C004EEDFB /* HideBook */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		287E94252DE5C64C004EEDFB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				287E94462DE5C65A004EEDFB /* Assets.xcassets in Resources */,
				287E94482DE5C65A004EEDFB /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		947356162A22AF74B66678F5 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-HideBook-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DEB3BF2DC944B30EB3FB4051 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HideBook/Pods-HideBook-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-HideBook/Pods-HideBook-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-HideBook/Pods-HideBook-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		287E94232DE5C64C004EEDFB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				287E94732DE5D0E2004EEDFB /* LeatherModel.swift in Sources */,
				287E94742DE5D0E2004EEDFB /* Constants.swift in Sources */,
				287E94752DE5D0E2004EEDFB /* MainTabBarController.swift in Sources */,
				287E94762DE5D0E2004EEDFB /* StockDetailViewController.swift in Sources */,
				287E94772DE5D0E2004EEDFB /* AddLeatherViewController.swift in Sources */,
				287E94782DE5D0E2004EEDFB /* AddUsageRecordViewController.swift in Sources */,
				287E94792DE5D0E2004EEDFB /* LeatherDataManager.swift in Sources */,
				287E947A2DE5D0E2004EEDFB /* StockUsageViewController.swift in Sources */,
				287E947B2DE5D0E2004EEDFB /* LeatherDetailViewController.swift in Sources */,
				287E947C2DE5D0E2004EEDFB /* UIView+Extensions.swift in Sources */,
				287E947D2DE5D0E2004EEDFB /* StockTableViewCell.swift in Sources */,
				287E947E2DE5D0E2004EEDFB /* UsageTableViewCell.swift in Sources */,
				287E947F2DE5D0E2004EEDFB /* UIColor+Extensions.swift in Sources */,
				287E94802DE5D0E2004EEDFB /* AppDelegate.swift in Sources */,
				287E94812DE5D0E2004EEDFB /* AddStockEntryViewController.swift in Sources */,
				287E94822DE5D0E2004EEDFB /* LeatherArchiveViewController.swift in Sources */,
				287E94832DE5D0E2004EEDFB /* LeatherTableViewCell.swift in Sources */,
				287E94842DE5D0E2004EEDFB /* StockStatisticsViewController.swift in Sources */,
				287E94852DE5D0E2004EEDFB /* StockDataManager.swift in Sources */,
				287E948A2DE5D4BE004EEDFB /* AnalyticsViewController.swift in Sources */,
				287E94862DE5D0E2004EEDFB /* GradientView.swift in Sources */,
				287E94872DE5D0E2004EEDFB /* UsageDetailViewController.swift in Sources */,
				287E94882DE5D0E2004EEDFB /* StockModel.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		287E94432DE5C65A004EEDFB /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				287E94422DE5C65A004EEDFB /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		287E943B2DE5C64D004EEDFB /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 65AFF5DA8214CDE49FA72B8A /* Pods-HideBook.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HideBook/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = HideBook;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = niucmd.HideBook;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		287E943C2DE5C64D004EEDFB /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 30030F43461AEABBEDD0346C /* Pods-HideBook.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = HideBook/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = HideBook;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = "";
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 14.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = niucmd.HideBook;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		287E943D2DE5C64D004EEDFB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		287E943E2DE5C64D004EEDFB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		287E94222DE5C64C004EEDFB /* Build configuration list for PBXProject "HideBook" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				287E943D2DE5C64D004EEDFB /* Debug */,
				287E943E2DE5C64D004EEDFB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		287E943A2DE5C64D004EEDFB /* Build configuration list for PBXNativeTarget "HideBook" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				287E943B2DE5C64D004EEDFB /* Debug */,
				287E943C2DE5C64D004EEDFB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 287E941F2DE5C64C004EEDFB /* Project object */;
}
