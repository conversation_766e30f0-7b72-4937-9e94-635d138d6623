# HideBook - 皮革资料库

一个专业的皮革资料管理iOS应用，帮助皮革工艺爱好者和专业人士系统化地管理皮革资料。

## 项目概述

HideBook是一个功能丰富的皮革资料库应用，支持皮革信息的添加、编辑、搜索和分类管理。应用采用现代化的iOS设计语言，提供直观的用户体验。

## 功能特性

### 模块一：皮革资料库（Leather Archive）

#### 核心功能
- ✅ **添加新皮革条目** - 支持详细的皮革信息录入
- ✅ **编辑、删除、复制已有皮革信息** - 完整的CRUD操作
- ✅ **搜索与筛选** - 支持按类型、手感、产地、厂牌等字段搜索
- ✅ **收藏功能** - 标记重要的皮革资料
- ✅ **图片支持** - 支持从相册导入或拍照添加皮革图片

#### 数据字段
每条皮革资料包含以下详细信息：

**基本信息**
- 名称（如：意大利植鞣革）
- 材质来源（牛皮/羊皮/猪皮/马臀皮/人造合成）
- 鞣制方式（植鞣/铬鞣/混合鞣）
- 表面处理（原色/压纹/光面/磨砂/漆面）
- 厚度（单位：mm）

**详细信息**
- 色号/颜色描述
- 产地/供应商/品牌
- 手感描述（硬挺/柔软/有弹性/湿润）
- 使用建议（适合制作哪些成品）

**附加信息**
- 图片上传（支持从相册导入）
- 价格信息
- 标签系统
- 备注说明
- 创建和更新时间

## 技术架构

### 开发环境
- **开发语言**: Swift 5.0+
- **最低支持版本**: iOS 14.0+
- **开发工具**: Xcode 16.0+

### 主要依赖
- **SnapKit**: 自动布局框架
- **IQKeyboardManagerSwift**: 键盘管理
- **AAInfographics**: 图表组件（为后续统计功能预留）

### 架构设计
- **MVC架构**: 清晰的模型-视图-控制器分离
- **数据持久化**: UserDefaults + JSON编码（支持后续升级到Core Data）
- **UI组件化**: 可复用的视图组件和扩展

## 项目结构

```
HideBook/
├── AppDelegate.swift              # 应用程序入口
├── Models/
│   ├── LeatherModel.swift         # 皮革数据模型
│   └── LeatherDataManager.swift   # 数据管理器
├── Views/
│   ├── LeatherTableViewCell.swift # 列表单元格
│   ├── GradientView.swift         # 渐变视图组件
│   └── UI Extensions/
├── Controllers/
│   ├── SimpleViewController.swift      # 当前简单视图（临时）
│   ├── LeatherArchiveViewController.swift    # 主列表页面（已完成）
│   ├── LeatherDetailViewController.swift     # 详情页面（已完成）
│   └── AddLeatherViewController.swift        # 添加/编辑页面（已完成）
├── Utils/
│   ├── Constants.swift            # 常量定义
│   ├── UIColor+Extensions.swift   # 颜色扩展
│   └── UIView+Extensions.swift    # 视图扩展
└── Resources/
    ├── Assets.xcassets           # 图片资源
    └── LaunchScreen.storyboard   # 启动页面
```

## 当前状态

### ✅ 已完成
1. **项目基础架构** - 完整的MVC架构搭建
2. **数据模型** - 完整的皮革数据模型和管理器
3. **UI组件** - 渐变视图、扩展工具类等
4. **核心视图控制器** - 列表、详情、添加/编辑页面
5. **基础功能** - CRUD操作、搜索、筛选、收藏等

### 🚧 当前显示
- 应用当前显示简单的欢迎界面
- 完整的皮革资料库功能已开发完成，但暂时使用简单视图作为入口

### 📋 下一步计划
1. **启用完整功能** - 将AppDelegate中的SimpleViewController替换为LeatherArchiveViewController
2. **UI优化** - 完善渐变色主题和动画效果
3. **数据预填充** - 添加更多示例皮革数据
4. **功能增强** - 添加导出、分享等功能

## 安装和运行

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd HideBook
   ```

2. **安装依赖**
   ```bash
   pod install
   ```

3. **打开项目**
   ```bash
   open HideBook.xcworkspace
   ```

4. **运行应用**
   - 选择iOS模拟器或真机
   - 点击运行按钮或使用 Cmd+R

## 设计特色

### 视觉设计
- **渐变色主题** - 优雅的蓝色渐变背景
- **卡片式布局** - 现代化的卡片设计
- **系统图标** - 丰富的SF Symbols图标使用
- **阴影效果** - 精致的阴影和圆角设计

### 用户体验
- **直观操作** - 简单易懂的界面设计
- **流畅动画** - 平滑的过渡动画效果
- **响应式布局** - 适配不同屏幕尺寸
- **无障碍支持** - 支持VoiceOver等辅助功能

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

[添加许可证信息]

---

**HideBook** - 让皮革资料管理变得简单而专业
