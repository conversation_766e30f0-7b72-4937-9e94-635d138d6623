//
//  StockUsageViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit
import AAInfographics

class StockUsageViewController: UIViewController {

    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    private var currentSegmentIndex = 0

    // MARK: - UI Components
    private let gradientBackgroundView = GradientView()
    private let segmentedControl = UISegmentedControl(items: ["Stock", "Usage"])
    private let quickStatsView = UIView()
    private let quickChartView = AAChartView()
    private let containerView = UIView()

    // Child View Controllers
    private let stockListViewController = StockListViewController()
    private let usageListViewController = UsageListViewController()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupSegmentedControl()
        setupChildViewControllers()
        showStockView()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        gradientBackgroundView.layoutSubviews()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Segmented Control
        segmentedControl.selectedSegmentIndex = 0
        segmentedControl.backgroundColor = .systemGray6
        segmentedControl.selectedSegmentTintColor = .appAccent
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        segmentedControl.setTitleTextAttributes([.foregroundColor: UIColor.label], for: .normal)

        // Container View
        containerView.backgroundColor = .clear

        // Quick Stats View
        setupQuickStatsView()

        view.addSubviews(gradientBackgroundView, segmentedControl, quickStatsView, containerView)
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        segmentedControl.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).inset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(32)
        }

        quickStatsView.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(120)
        }

        containerView.snp.makeConstraints { make in
            make.top.equalTo(quickStatsView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalTo(view.safeAreaLayoutGuide)
        }
    }

    private func setupNavigationBar() {
        title = "Stock & Usage"
        navigationController?.navigationBar.prefersLargeTitles = true

        // Add button
        let addButton = UIBarButtonItem(
            barButtonSystemItem: .add,
            target: self,
            action: #selector(addButtonTapped)
        )

        // Statistics button
        let statsButton = UIBarButtonItem(
            image: UIImage(systemName: "chart.bar"),
            style: .plain,
            target: self,
            action: #selector(statsButtonTapped)
        )

        // Analytics button
        let analyticsButton = UIBarButtonItem(
            image: UIImage(systemName: "chart.line.uptrend.xyaxis"),
            style: .plain,
            target: self,
            action: #selector(analyticsButtonTapped)
        )

        navigationItem.rightBarButtonItems = [addButton, statsButton, analyticsButton]
    }

    private func setupSegmentedControl() {
        segmentedControl.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
    }

    private func setupChildViewControllers() {
        // Add child view controllers
        addChild(stockListViewController)
        addChild(usageListViewController)

        containerView.addSubview(stockListViewController.view)
        containerView.addSubview(usageListViewController.view)

        stockListViewController.view.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        usageListViewController.view.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        stockListViewController.didMove(toParent: self)
        usageListViewController.didMove(toParent: self)

        // Initially hide usage view
        usageListViewController.view.isHidden = true
    }

    // MARK: - Actions
    @objc private func segmentChanged() {
        currentSegmentIndex = segmentedControl.selectedSegmentIndex

        UIView.transition(with: containerView, duration: 0.3, options: .transitionCrossDissolve) {
            if self.currentSegmentIndex == 0 {
                self.showStockView()
            } else {
                self.showUsageView()
            }
        }
    }

    @objc private func addButtonTapped() {
        if currentSegmentIndex == 0 {
            // Add stock entry
            let addStockVC = AddStockEntryViewController()
            let navController = UINavigationController(rootViewController: addStockVC)
            present(navController, animated: true)
        } else {
            // Add usage record
            let addUsageVC = AddUsageRecordViewController()
            let navController = UINavigationController(rootViewController: addUsageVC)
            present(navController, animated: true)
        }
    }

    @objc private func statsButtonTapped() {
        let statsVC = StockStatisticsViewController()
        let navController = UINavigationController(rootViewController: statsVC)
        present(navController, animated: true)
    }

    @objc private func analyticsButtonTapped() {
        let analyticsVC = AnalyticsViewController()
        let navController = UINavigationController(rootViewController: analyticsVC)
        present(navController, animated: true)
    }

    // MARK: - View Management
    private func showStockView() {
        stockListViewController.view.isHidden = false
        usageListViewController.view.isHidden = true
        stockListViewController.view.alpha = 1.0
        usageListViewController.view.alpha = 0.0
        updateQuickChart()
    }

    private func showUsageView() {
        stockListViewController.view.isHidden = true
        usageListViewController.view.isHidden = false
        stockListViewController.view.alpha = 0.0
        usageListViewController.view.alpha = 1.0
        updateQuickChart()
    }

    // MARK: - Quick Stats Setup
    private func setupQuickStatsView() {
        quickStatsView.backgroundColor = .appCardBackground
        quickStatsView.setCornerRadius(Constants.CornerRadius.medium)
        quickStatsView.applyCardShadow()

        quickChartView.backgroundColor = .clear
        quickChartView.setCornerRadius(Constants.CornerRadius.small)

        quickStatsView.addSubview(quickChartView)

        quickChartView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(Constants.Spacing.small)
        }

        updateQuickChart()
    }

    private func updateQuickChart() {
        if currentSegmentIndex == 0 {
            setupStockQuickChart()
        } else {
            setupUsageQuickChart()
        }
    }

    private func setupStockQuickChart() {
        let stockEntries = stockDataManager.allStockEntries
        let inStockCount = stockEntries.filter { $0.stockStatus == "In Stock" }.count
        let lowStockCount = stockEntries.filter { $0.stockStatus == "Low Stock" }.count
        let outOfStockCount = stockEntries.filter { $0.stockStatus == "Out of Stock" }.count

        let chartModel = AAChartModel()
            .chartType(.donut)
            .title("")
            .subtitle("")
            .dataLabelsEnabled(true)
            .legendEnabled(true)
            .series([
                AASeriesElement()
                    .name("Stock Status")
                    .data([
                        ["In Stock", inStockCount],
                        ["Low Stock", lowStockCount],
                        ["Out of Stock", outOfStockCount]
                    ])
                    .colorByPoint(true)
            ])
            .colors(["#28a745", "#ffc107", "#dc3545"])
            .backgroundColor("#00000000")

        quickChartView.aa_drawChartWithChartModel(chartModel)
    }

    private func setupUsageQuickChart() {
        let usageRecords = stockDataManager.allUsageRecords
        let categories = Constants.Stock.usageCategories.prefix(5) // Show top 5 categories
        let categoryUsage = categories.map { category in
            usageRecords.filter { $0.category == category }
                .reduce(0.0) { $0 + $1.quantityUsed }
        }

        let chartModel = AAChartModel()
            .chartType(.column)
            .title("")
            .subtitle("")
            .dataLabelsEnabled(false)
            .legendEnabled(false)
            .yAxisTitle("")
            .categories(Array(categories))
            .series([
                AASeriesElement()
                    .name("Usage")
                    .data(categoryUsage)
                    .color("#17a2b8")
            ])
            .backgroundColor("#00000000")

        quickChartView.aa_drawChartWithChartModel(chartModel)
    }
}

// MARK: - Stock List View Controller
class StockListViewController: UIViewController {

    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    private var stockEntries: [StockEntry] = []
    private var filteredStockEntries: [StockEntry] = []
    private var isSearching = false
    private var currentSortType: StockSortType = .purchaseDateDescending

    // MARK: - UI Components
    private let tableView = UITableView()
    private let searchController = UISearchController(searchResultsController: nil)
    private let emptyStateView = UIView()
    private let emptyImageView = UIImageView()
    private let emptyTitleLabel = UILabel()
    private let emptySubtitleLabel = UILabel()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupSearchController()
        setupDataManager()
        loadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .clear

        // Table View
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(StockTableViewCell.self, forCellReuseIdentifier: StockTableViewCell.identifier)
        tableView.contentInset = UIEdgeInsets(top: Constants.Spacing.small, left: 0, bottom: Constants.Spacing.large, right: 0)

        // Empty State View
        setupEmptyStateView()

        view.addSubviews(tableView, emptyStateView)
    }

    private func setupEmptyStateView() {
        emptyStateView.isHidden = true

        emptyImageView.image = UIImage(systemName: "cube.box")
        emptyImageView.tintColor = .systemGray3
        emptyImageView.contentMode = .scaleAspectFit

        emptyTitleLabel.text = "No Stock Entries"
        emptyTitleLabel.font = Constants.Fonts.title2
        emptyTitleLabel.textColor = .appTextSecondary
        emptyTitleLabel.textAlignment = .center

        emptySubtitleLabel.text = "Tap the + button to add your first stock entry"
        emptySubtitleLabel.font = Constants.Fonts.body
        emptySubtitleLabel.textColor = .appTextTertiary
        emptySubtitleLabel.textAlignment = .center
        emptySubtitleLabel.numberOfLines = 0

        emptyStateView.addSubviews(emptyImageView, emptyTitleLabel, emptySubtitleLabel)
    }

    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.large)
        }

        emptyImageView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }

        emptyTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyImageView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview()
        }

        emptySubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyTitleLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }

    private func setupSearchController() {
        searchController.searchResultsUpdater = self
        searchController.obscuresBackgroundDuringPresentation = false
        searchController.searchBar.placeholder = "Search stock entries..."
        searchController.searchBar.searchBarStyle = .minimal

        navigationItem.searchController = searchController
        definesPresentationContext = true
    }

    private func setupDataManager() {
        stockDataManager.delegate = self
    }

    // MARK: - Data Loading
    private func loadData() {
        stockEntries = stockDataManager.sortedStockEntries(by: currentSortType)
        updateFilteredStockEntries()
        updateEmptyState()
        tableView.reloadData()
    }

    private func updateFilteredStockEntries() {
        if isSearching {
            let searchText = searchController.searchBar.text ?? ""
            filteredStockEntries = stockDataManager.searchStockEntries(with: searchText)
        } else {
            filteredStockEntries = stockEntries
        }
    }

    private func updateEmptyState() {
        let isEmpty = filteredStockEntries.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty

        if isEmpty && isSearching {
            emptyTitleLabel.text = "No Stock Found"
            emptySubtitleLabel.text = "Try using different keywords"
            emptyImageView.image = UIImage(systemName: "magnifyingglass")
        } else if isEmpty {
            emptyTitleLabel.text = "No Stock Entries"
            emptySubtitleLabel.text = "Tap the + button to add your first stock entry"
            emptyImageView.image = UIImage(systemName: "cube.box")
        }
    }
}

// MARK: - UITableViewDataSource
extension StockListViewController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return filteredStockEntries.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: StockTableViewCell.identifier, for: indexPath) as? StockTableViewCell else {
            return UITableViewCell()
        }

        let stockEntry = filteredStockEntries[indexPath.row]
        cell.configure(with: stockEntry)

        return cell
    }
}

// MARK: - UITableViewDelegate
extension StockListViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let stockEntry = filteredStockEntries[indexPath.row]
        let detailVC = StockDetailViewController(stockEntry: stockEntry)
        navigationController?.pushViewController(detailVC, animated: true)
    }

    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let stockEntry = filteredStockEntries[indexPath.row]

        // Delete action
        let deleteAction = UIContextualAction(style: .destructive, title: "Delete") { _, _, completion in
            self.showDeleteConfirmation(for: stockEntry, at: indexPath)
            completion(true)
        }
        deleteAction.image = UIImage(systemName: "trash")

        // Edit action
        let editAction = UIContextualAction(style: .normal, title: "Edit") { _, _, completion in
            self.editStockEntry(stockEntry)
            completion(true)
        }
        editAction.image = UIImage(systemName: "pencil")
        editAction.backgroundColor = .systemOrange

        // Add usage action
        let addUsageAction = UIContextualAction(style: .normal, title: "Use") { _, _, completion in
            self.addUsageRecord(for: stockEntry)
            completion(true)
        }
        addUsageAction.image = UIImage(systemName: "minus.circle")
        addUsageAction.backgroundColor = .systemBlue

        return UISwipeActionsConfiguration(actions: [deleteAction, editAction, addUsageAction])
    }

    // MARK: - Swipe Actions
    private func showDeleteConfirmation(for stockEntry: StockEntry, at indexPath: IndexPath) {
        let alertController = UIAlertController(
            title: "Delete Stock Entry",
            message: "Are you sure you want to delete this stock entry for \(stockEntry.leatherName)? This action cannot be undone.",
            preferredStyle: .alert
        )

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alertController.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
            self.deleteStockEntry(stockEntry, at: indexPath)
        })

        present(alertController, animated: true)
    }

    private func deleteStockEntry(_ stockEntry: StockEntry, at indexPath: IndexPath) {
        stockDataManager.deleteStockEntry(withId: stockEntry.id)

        // Animate removal
        filteredStockEntries.remove(at: indexPath.row)
        tableView.deleteRows(at: [indexPath], with: .fade)

        // Update empty state
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.updateEmptyState()
        }
    }

    private func editStockEntry(_ stockEntry: StockEntry) {
        let editVC = AddStockEntryViewController(stockEntry: stockEntry)
        let navController = UINavigationController(rootViewController: editVC)
        present(navController, animated: true)
    }

    private func addUsageRecord(for stockEntry: StockEntry) {
        let addUsageVC = AddUsageRecordViewController(stockEntry: stockEntry)
        let navController = UINavigationController(rootViewController: addUsageVC)
        present(navController, animated: true)
    }
}

// MARK: - UISearchResultsUpdating
extension StockListViewController: UISearchResultsUpdating {

    func updateSearchResults(for searchController: UISearchController) {
        let searchText = searchController.searchBar.text ?? ""
        isSearching = !searchText.isEmpty

        updateFilteredStockEntries()
        updateEmptyState()
        tableView.reloadData()
    }
}

// MARK: - StockDataManagerDelegate
extension StockListViewController: StockDataManagerDelegate {

    func dataManagerDidUpdateStock(_ manager: StockDataManager) {
        DispatchQueue.main.async {
            self.loadData()
        }
    }

    func dataManager(_ manager: StockDataManager, didFailWithError error: Error) {
        DispatchQueue.main.async {
            let alertController = UIAlertController(
                title: "Error",
                message: "Data operation failed: \(error.localizedDescription)",
                preferredStyle: .alert
            )
            alertController.addAction(UIAlertAction(title: "OK", style: .default))
            self.present(alertController, animated: true)
        }
    }
}

// MARK: - Usage List View Controller
class UsageListViewController: UIViewController {

    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    private var usageRecords: [UsageRecord] = []
    private var filteredUsageRecords: [UsageRecord] = []
    private var isSearching = false

    // MARK: - UI Components
    private let tableView = UITableView()
    private let searchController = UISearchController(searchResultsController: nil)
    private let emptyStateView = UIView()
    private let emptyImageView = UIImageView()
    private let emptyTitleLabel = UILabel()
    private let emptySubtitleLabel = UILabel()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupSearchController()
        setupDataManager()
        loadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .clear

        // Table View
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(UsageTableViewCell.self, forCellReuseIdentifier: UsageTableViewCell.identifier)
        tableView.contentInset = UIEdgeInsets(top: Constants.Spacing.small, left: 0, bottom: Constants.Spacing.large, right: 0)

        // Empty State View
        setupEmptyStateView()

        view.addSubviews(tableView, emptyStateView)
    }

    private func setupEmptyStateView() {
        emptyStateView.isHidden = true

        emptyImageView.image = UIImage(systemName: "list.clipboard")
        emptyImageView.tintColor = .systemGray3
        emptyImageView.contentMode = .scaleAspectFit

        emptyTitleLabel.text = "No Usage Records"
        emptyTitleLabel.font = Constants.Fonts.title2
        emptyTitleLabel.textColor = .appTextSecondary
        emptyTitleLabel.textAlignment = .center

        emptySubtitleLabel.text = "Start using your leather stock to see usage history here"
        emptySubtitleLabel.font = Constants.Fonts.body
        emptySubtitleLabel.textColor = .appTextTertiary
        emptySubtitleLabel.textAlignment = .center
        emptySubtitleLabel.numberOfLines = 0

        emptyStateView.addSubviews(emptyImageView, emptyTitleLabel, emptySubtitleLabel)
    }

    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.large)
        }

        emptyImageView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }

        emptyTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyImageView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview()
        }

        emptySubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyTitleLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.bottom.equalToSuperview()
        }
    }

    private func setupSearchController() {
        searchController.searchResultsUpdater = self
        searchController.obscuresBackgroundDuringPresentation = false
        searchController.searchBar.placeholder = "Search usage records..."
        searchController.searchBar.searchBarStyle = .minimal

        navigationItem.searchController = searchController
        definesPresentationContext = true
    }

    private func setupDataManager() {
        stockDataManager.delegate = self
    }

    // MARK: - Data Loading
    private func loadData() {
        usageRecords = stockDataManager.allUsageRecords.sorted { $0.usageDate > $1.usageDate }
        updateFilteredUsageRecords()
        updateEmptyState()
        tableView.reloadData()
    }

    private func updateFilteredUsageRecords() {
        if isSearching {
            let searchText = searchController.searchBar.text ?? ""
            filteredUsageRecords = stockDataManager.searchUsageRecords(with: searchText)
        } else {
            filteredUsageRecords = usageRecords
        }
    }

    private func updateEmptyState() {
        let isEmpty = filteredUsageRecords.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty

        if isEmpty && isSearching {
            emptyTitleLabel.text = "No Usage Found"
            emptySubtitleLabel.text = "Try using different keywords"
            emptyImageView.image = UIImage(systemName: "magnifyingglass")
        } else if isEmpty {
            emptyTitleLabel.text = "No Usage Records"
            emptySubtitleLabel.text = "Start using your leather stock to see usage history here"
            emptyImageView.image = UIImage(systemName: "list.clipboard")
        }
    }
}

// MARK: - UITableViewDataSource
extension UsageListViewController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return filteredUsageRecords.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: UsageTableViewCell.identifier, for: indexPath) as? UsageTableViewCell else {
            return UITableViewCell()
        }

        let usageRecord = filteredUsageRecords[indexPath.row]
        cell.configure(with: usageRecord)

        return cell
    }
}

// MARK: - UITableViewDelegate
extension UsageListViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 100
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        let usageRecord = filteredUsageRecords[indexPath.row]
        let detailVC = UsageDetailViewController(usageRecord: usageRecord)
        navigationController?.pushViewController(detailVC, animated: true)
    }

    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let usageRecord = filteredUsageRecords[indexPath.row]

        // Delete action
        let deleteAction = UIContextualAction(style: .destructive, title: "Delete") { _, _, completion in
            self.showDeleteConfirmation(for: usageRecord, at: indexPath)
            completion(true)
        }
        deleteAction.image = UIImage(systemName: "trash")

        // Edit action
        let editAction = UIContextualAction(style: .normal, title: "Edit") { _, _, completion in
            self.editUsageRecord(usageRecord)
            completion(true)
        }
        editAction.image = UIImage(systemName: "pencil")
        editAction.backgroundColor = .systemOrange

        return UISwipeActionsConfiguration(actions: [deleteAction, editAction])
    }

    // MARK: - Swipe Actions
    private func showDeleteConfirmation(for usageRecord: UsageRecord, at indexPath: IndexPath) {
        let alertController = UIAlertController(
            title: "Delete Usage Record",
            message: "Are you sure you want to delete this usage record for \(usageRecord.projectName)?",
            preferredStyle: .alert
        )

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alertController.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
            self.deleteUsageRecord(usageRecord, at: indexPath)
        })

        present(alertController, animated: true)
    }

    private func deleteUsageRecord(_ usageRecord: UsageRecord, at indexPath: IndexPath) {
        stockDataManager.deleteUsageRecord(withId: usageRecord.id)

        // Animate removal
        filteredUsageRecords.remove(at: indexPath.row)
        tableView.deleteRows(at: [indexPath], with: .fade)

        // Update empty state
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.updateEmptyState()
        }
    }

    private func editUsageRecord(_ usageRecord: UsageRecord) {
        let editVC = AddUsageRecordViewController(usageRecord: usageRecord)
        let navController = UINavigationController(rootViewController: editVC)
        present(navController, animated: true)
    }
}

// MARK: - UISearchResultsUpdating
extension UsageListViewController: UISearchResultsUpdating {

    func updateSearchResults(for searchController: UISearchController) {
        let searchText = searchController.searchBar.text ?? ""
        isSearching = !searchText.isEmpty

        updateFilteredUsageRecords()
        updateEmptyState()
        tableView.reloadData()
    }
}

// MARK: - StockDataManagerDelegate
extension UsageListViewController: StockDataManagerDelegate {

    func dataManagerDidUpdateStock(_ manager: StockDataManager) {
        DispatchQueue.main.async {
            self.loadData()
        }
    }

    func dataManager(_ manager: StockDataManager, didFailWithError error: Error) {
        DispatchQueue.main.async {
            let alertController = UIAlertController(
                title: "Error",
                message: "Data operation failed: \(error.localizedDescription)",
                preferredStyle: .alert
            )
            alertController.addAction(UIAlertAction(title: "OK", style: .default))
            self.present(alertController, animated: true)
        }
    }
}