//
//  AppDelegate.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import IQKeyboardManagerSwift

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {

        // Configure IQKeyboardManager
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardManager.shared.resignOnTouchOutside = true

        // Create main window
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.backgroundColor = UIColor.systemBackground

        // Setup root view controller
        let mainViewController = MainTabBarController()

        window?.rootViewController = mainViewController
        window?.makeKeyAndVisible()

        return true
    }

}

