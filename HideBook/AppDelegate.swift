//
//  AppDelegate.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import IQKeyboardManagerSwift
import IQK<PERSON>boardToolbarManager

@main
class AppDelegate: UIResponder, UIApplicationDelegate {

    var window: UIWindow?

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> <PERSON>ol {

        // Configure IQKeyboardManager
        IQKeyboardManager.shared.isEnabled = true
        IQKeyboardToolbarManager.shared.isEnabled = true

        // Create main window
        window = UIWindow(frame: UIScreen.main.bounds)
        window?.backgroundColor = UIColor.systemBackground

        // Setup root view controller
        let mainViewController = MainTabBarController()
        

        window?.rootViewController = mainViewController
        window?.makeKeyAndVisible()

        return true
    }

}

