//
//  StockModel.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

// MARK: - Stock Entry Model
struct StockEntry: Codable, Equatable {

    // MARK: - Properties
    let id: String
    let leatherId: String // Reference to LeatherModel
    var currentQuantity: Double // Current remaining quantity
    var areaUnit: String // sq ft, sq m, sq cm
    var unitPrice: Double // Price per unit
    var totalCost: Double // Total cost of purchase
    var currency: String // Currency symbol
    var supplier: String // Supplier/source
    var batchNumber: String // Batch identifier
    var purchaseDate: Date
    var lowStockThreshold: Double // Alert threshold
    var notes: String // Additional notes
    var createdAt: Date
    var updatedAt: Date

    // MARK: - Computed Properties
    var leatherName: String {
        // Use a simple fallback to avoid circular dependency during initialization
        return "Leather Stock"
    }

    var stockStatus: String {
        if currentQuantity <= 0 {
            return "Out of Stock"
        } else if currentQuantity <= lowStockThreshold {
            return "Low Stock"
        } else {
            return "In Stock"
        }
    }

    var isLowStock: Bool {
        return currentQuantity > 0 && currentQuantity <= lowStockThreshold
    }

    var isOutOfStock: Bool {
        return currentQuantity <= 0
    }

    var formattedQuantity: String {
        return String(format: "%.2f %@", currentQuantity, areaUnit)
    }

    var formattedUnitPrice: String {
        return String(format: "%@%.2f/%@", currency, unitPrice, areaUnit)
    }

    var formattedTotalCost: String {
        return String(format: "%@%.2f", currency, totalCost)
    }

    var formattedPurchaseDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: purchaseDate)
    }

    // MARK: - Initialization
    init(
        id: String = UUID().uuidString,
        leatherId: String,
        currentQuantity: Double,
        areaUnit: String = "sq ft",
        unitPrice: Double,
        totalCost: Double? = nil,
        currency: String = "$",
        supplier: String = "",
        batchNumber: String = "",
        purchaseDate: Date = Date(),
        lowStockThreshold: Double = Constants.Stock.defaultLowStockThreshold,
        notes: String = ""
    ) {
        self.id = id
        self.leatherId = leatherId
        self.currentQuantity = currentQuantity
        self.areaUnit = areaUnit
        self.unitPrice = unitPrice
        self.totalCost = totalCost ?? (unitPrice * currentQuantity)
        self.currency = currency
        self.supplier = supplier
        self.batchNumber = batchNumber
        self.purchaseDate = purchaseDate
        self.lowStockThreshold = lowStockThreshold
        self.notes = notes
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    // MARK: - Methods
    mutating func updateQuantity(_ newQuantity: Double) {
        currentQuantity = newQuantity
        updatedAt = Date()
    }

    mutating func updateTimestamp() {
        updatedAt = Date()
    }

    // MARK: - Validation
    var isValid: Bool {
        return !leatherId.isEmpty &&
               currentQuantity >= 0 &&
               unitPrice >= 0 &&
               totalCost >= 0 &&
               !areaUnit.isEmpty &&
               !currency.isEmpty
    }

    var validationErrors: [String] {
        var errors: [String] = []

        if leatherId.isEmpty {
            errors.append("Leather reference is required")
        }

        if currentQuantity < 0 {
            errors.append("Quantity cannot be negative")
        }

        if unitPrice < 0 {
            errors.append("Unit price cannot be negative")
        }

        if totalCost < 0 {
            errors.append("Total cost cannot be negative")
        }

        if areaUnit.isEmpty {
            errors.append("Area unit is required")
        }

        if currency.isEmpty {
            errors.append("Currency is required")
        }

        return errors
    }
}

// MARK: - Usage Record Model
struct UsageRecord: Codable, Equatable {

    // MARK: - Properties
    let id: String
    let stockId: String // Reference to StockEntry
    var projectName: String // Name of the project/item made
    var category: String // Wallet, Bag, Belt, etc.
    var quantityUsed: Double // Amount used
    var remainingAfterUse: Double // Remaining quantity after this usage
    var usageDate: Date
    var notes: String // Additional notes about usage
    var createdAt: Date

    // MARK: - Computed Properties
    var formattedQuantityUsed: String {
        return String(format: "%.2f sq ft", quantityUsed)
    }

    var formattedRemainingAfterUse: String {
        return String(format: "%.2f sq ft", remainingAfterUse)
    }

    var formattedUsageDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: usageDate)
    }

    var leatherName: String {
        return "Leather Usage"
    }

    // MARK: - Initialization
    init(
        id: String = UUID().uuidString,
        stockId: String,
        projectName: String,
        category: String = "Accessory",
        quantityUsed: Double,
        remainingAfterUse: Double,
        usageDate: Date = Date(),
        notes: String = ""
    ) {
        self.id = id
        self.stockId = stockId
        self.projectName = projectName
        self.category = category
        self.quantityUsed = quantityUsed
        self.remainingAfterUse = remainingAfterUse
        self.usageDate = usageDate
        self.notes = notes
        self.createdAt = Date()
    }

    // MARK: - Validation
    var isValid: Bool {
        return !stockId.isEmpty &&
               !projectName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               quantityUsed > 0 &&
               remainingAfterUse >= 0
    }

    var validationErrors: [String] {
        var errors: [String] = []

        if stockId.isEmpty {
            errors.append("Stock reference is required")
        }

        if projectName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("Project name is required")
        }

        if quantityUsed <= 0 {
            errors.append("Quantity used must be greater than 0")
        }

        if remainingAfterUse < 0 {
            errors.append("Remaining quantity cannot be negative")
        }

        return errors
    }
}


