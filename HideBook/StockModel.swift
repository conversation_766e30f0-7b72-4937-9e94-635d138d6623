//
//  StockModel.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

// MARK: - Stock Entry Model
struct StockEntry: Codable, Equatable {
    
    // MARK: - Properties
    let id: String
    let leatherId: String // Reference to LeatherModel
    var currentQuantity: Double // Current remaining quantity
    var areaUnit: String // sq ft, sq m, sq cm
    var unitPrice: Double // Price per unit
    var totalCost: Double // Total cost of purchase
    var currency: String // Currency symbol
    var supplier: String // Supplier/source
    var batchNumber: String // Batch identifier
    var purchaseDate: Date
    var lowStockThreshold: Double // Alert threshold
    var notes: String // Additional notes
    var createdAt: Date
    var updatedAt: Date
    
    // MARK: - Computed Properties
    var leatherName: String {
        return LeatherDataManager.shared.getLeather(withId: leatherId)?.name ?? "Unknown Leather"
    }
    
    var stockStatus: String {
        if currentQuantity <= 0 {
            return "Out of Stock"
        } else if currentQuantity <= lowStockThreshold {
            return "Low Stock"
        } else {
            return "In Stock"
        }
    }
    
    var isLowStock: Bool {
        return currentQuantity > 0 && currentQuantity <= lowStockThreshold
    }
    
    var isOutOfStock: Bool {
        return currentQuantity <= 0
    }
    
    var formattedQuantity: String {
        return String(format: "%.2f %@", currentQuantity, areaUnit)
    }
    
    var formattedUnitPrice: String {
        return String(format: "%@%.2f/%@", currency, unitPrice, areaUnit)
    }
    
    var formattedTotalCost: String {
        return String(format: "%@%.2f", currency, totalCost)
    }
    
    var formattedPurchaseDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: purchaseDate)
    }
    
    // MARK: - Initialization
    init(
        id: String = UUID().uuidString,
        leatherId: String,
        currentQuantity: Double,
        areaUnit: String = "sq ft",
        unitPrice: Double,
        totalCost: Double? = nil,
        currency: String = "$",
        supplier: String = "",
        batchNumber: String = "",
        purchaseDate: Date = Date(),
        lowStockThreshold: Double = Constants.Stock.defaultLowStockThreshold,
        notes: String = ""
    ) {
        self.id = id
        self.leatherId = leatherId
        self.currentQuantity = currentQuantity
        self.areaUnit = areaUnit
        self.unitPrice = unitPrice
        self.totalCost = totalCost ?? (unitPrice * currentQuantity)
        self.currency = currency
        self.supplier = supplier
        self.batchNumber = batchNumber
        self.purchaseDate = purchaseDate
        self.lowStockThreshold = lowStockThreshold
        self.notes = notes
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    // MARK: - Methods
    mutating func updateQuantity(_ newQuantity: Double) {
        currentQuantity = newQuantity
        updatedAt = Date()
    }
    
    mutating func updateTimestamp() {
        updatedAt = Date()
    }
    
    // MARK: - Validation
    var isValid: Bool {
        return !leatherId.isEmpty &&
               currentQuantity >= 0 &&
               unitPrice >= 0 &&
               totalCost >= 0 &&
               !areaUnit.isEmpty &&
               !currency.isEmpty
    }
    
    var validationErrors: [String] {
        var errors: [String] = []
        
        if leatherId.isEmpty {
            errors.append("Leather reference is required")
        }
        
        if currentQuantity < 0 {
            errors.append("Quantity cannot be negative")
        }
        
        if unitPrice < 0 {
            errors.append("Unit price cannot be negative")
        }
        
        if totalCost < 0 {
            errors.append("Total cost cannot be negative")
        }
        
        if areaUnit.isEmpty {
            errors.append("Area unit is required")
        }
        
        if currency.isEmpty {
            errors.append("Currency is required")
        }
        
        return errors
    }
}

// MARK: - Usage Record Model
struct UsageRecord: Codable, Equatable {
    
    // MARK: - Properties
    let id: String
    let stockId: String // Reference to StockEntry
    var projectName: String // Name of the project/item made
    var category: String // Wallet, Bag, Belt, etc.
    var quantityUsed: Double // Amount used
    var remainingAfterUse: Double // Remaining quantity after this usage
    var usageDate: Date
    var notes: String // Additional notes about usage
    var createdAt: Date
    
    // MARK: - Computed Properties
    var formattedQuantityUsed: String {
        guard let stock = StockDataManager.shared.getStockEntry(withId: stockId) else {
            return String(format: "%.2f", quantityUsed)
        }
        return String(format: "%.2f %@", quantityUsed, stock.areaUnit)
    }
    
    var formattedRemainingAfterUse: String {
        guard let stock = StockDataManager.shared.getStockEntry(withId: stockId) else {
            return String(format: "%.2f", remainingAfterUse)
        }
        return String(format: "%.2f %@", remainingAfterUse, stock.areaUnit)
    }
    
    var formattedUsageDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: usageDate)
    }
    
    var leatherName: String {
        guard let stock = StockDataManager.shared.getStockEntry(withId: stockId) else {
            return "Unknown"
        }
        return stock.leatherName
    }
    
    // MARK: - Initialization
    init(
        id: String = UUID().uuidString,
        stockId: String,
        projectName: String,
        category: String = "Accessory",
        quantityUsed: Double,
        remainingAfterUse: Double,
        usageDate: Date = Date(),
        notes: String = ""
    ) {
        self.id = id
        self.stockId = stockId
        self.projectName = projectName
        self.category = category
        self.quantityUsed = quantityUsed
        self.remainingAfterUse = remainingAfterUse
        self.usageDate = usageDate
        self.notes = notes
        self.createdAt = Date()
    }
    
    // MARK: - Validation
    var isValid: Bool {
        return !stockId.isEmpty &&
               !projectName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               quantityUsed > 0 &&
               remainingAfterUse >= 0
    }
    
    var validationErrors: [String] {
        var errors: [String] = []
        
        if stockId.isEmpty {
            errors.append("Stock reference is required")
        }
        
        if projectName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("Project name is required")
        }
        
        if quantityUsed <= 0 {
            errors.append("Quantity used must be greater than 0")
        }
        
        if remainingAfterUse < 0 {
            errors.append("Remaining quantity cannot be negative")
        }
        
        return errors
    }
}

// MARK: - Sample Data
extension StockEntry {
    static func sampleData() -> [StockEntry] {
        let leathers = LeatherDataManager.shared.allLeathers
        guard !leathers.isEmpty else { return [] }
        
        return [
            StockEntry(
                leatherId: leathers[0].id,
                currentQuantity: 25.5,
                areaUnit: "sq ft",
                unitPrice: 12.50,
                currency: "$",
                supplier: "Conceria Santa Croce",
                batchNumber: "VSC-2024-001",
                purchaseDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
                lowStockThreshold: 5.0,
                notes: "High quality vegetable tanned leather"
            ),
            StockEntry(
                leatherId: leathers.count > 1 ? leathers[1].id : leathers[0].id,
                currentQuantity: 8.2,
                areaUnit: "sq ft",
                unitPrice: 45.00,
                currency: "$",
                supplier: "Shinki Leather",
                batchNumber: "SHK-2024-002",
                purchaseDate: Calendar.current.date(byAdding: .day, value: -15, to: Date()) ?? Date(),
                lowStockThreshold: 10.0,
                notes: "Premium shell cordovan"
            )
        ]
    }
}

extension UsageRecord {
    static func sampleData() -> [UsageRecord] {
        let stocks = StockDataManager.shared.allStockEntries
        guard !stocks.isEmpty else { return [] }
        
        return [
            UsageRecord(
                stockId: stocks[0].id,
                projectName: "Bifold Wallet",
                category: "Wallet",
                quantityUsed: 2.5,
                remainingAfterUse: 23.0,
                usageDate: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
                notes: "Client commission - brown wallet"
            ),
            UsageRecord(
                stockId: stocks[0].id,
                projectName: "Card Holder",
                category: "Wallet",
                quantityUsed: 1.2,
                remainingAfterUse: 21.8,
                usageDate: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
                notes: "Personal use"
            )
        ]
    }
}
