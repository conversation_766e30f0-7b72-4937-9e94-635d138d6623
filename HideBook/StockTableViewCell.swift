//
//  StockTableViewCell.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class StockTableViewCell: UITableViewCell {
    
    // MARK: - Properties
    static let identifier = "StockTableViewCell"
    
    // MARK: - UI Components
    private let containerView = UIView()
    private let gradientView = GradientView()
    private let leatherNameLabel = UILabel()
    private let quantityLabel = UILabel()
    private let statusLabel = UILabel()
    private let statusIndicatorView = UIView()
    private let supplierLabel = UILabel()
    private let purchaseDateLabel = UILabel()
    private let priceLabel = UILabel()
    private let detailStackView = UIStackView()
    private let topStackView = UIStackView()
    private let bottomStackView = UIStackView()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func prepareForReuse() {
        super.prepareForReuse()
        leatherNameLabel.text = nil
        quantityLabel.text = nil
        statusLabel.text = nil
        supplierLabel.text = nil
        purchaseDateLabel.text = nil
        priceLabel.text = nil
        statusIndicatorView.backgroundColor = .systemGray
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        gradientView.layoutSubviews()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // Container View
        containerView.backgroundColor = .appCardBackground
        containerView.setCornerRadius(Constants.CornerRadius.medium)
        containerView.applyCardShadow()
        
        // Gradient View
        gradientView.setPrimaryGradient()
        gradientView.setCornerRadius(Constants.CornerRadius.medium)
        gradientView.alpha = 0.1
        
        // Leather Name Label
        leatherNameLabel.font = Constants.Fonts.headline
        leatherNameLabel.textColor = .appTextPrimary
        leatherNameLabel.numberOfLines = 1
        
        // Quantity Label
        quantityLabel.font = Constants.Fonts.title3
        quantityLabel.textColor = .appAccent
        quantityLabel.textAlignment = .right
        
        // Status Label
        statusLabel.font = Constants.Fonts.caption1
        statusLabel.textColor = .white
        statusLabel.textAlignment = .center
        statusLabel.backgroundColor = .systemGray
        statusLabel.setCornerRadius(Constants.CornerRadius.small / 2)
        
        // Status Indicator View
        statusIndicatorView.setCornerRadius(4)
        statusIndicatorView.backgroundColor = .systemGray
        
        // Supplier Label
        supplierLabel.font = Constants.Fonts.subheadline
        supplierLabel.textColor = .appTextSecondary
        supplierLabel.numberOfLines = 1
        
        // Purchase Date Label
        purchaseDateLabel.font = Constants.Fonts.caption1
        purchaseDateLabel.textColor = .appTextTertiary
        
        // Price Label
        priceLabel.font = Constants.Fonts.callout
        priceLabel.textColor = .appTextSecondary
        priceLabel.textAlignment = .right
        
        // Stack Views
        topStackView.axis = .horizontal
        topStackView.spacing = Constants.Spacing.small
        topStackView.alignment = .center
        topStackView.distribution = .fill
        
        bottomStackView.axis = .horizontal
        bottomStackView.spacing = Constants.Spacing.small
        bottomStackView.alignment = .center
        bottomStackView.distribution = .fill
        
        detailStackView.axis = .vertical
        detailStackView.spacing = Constants.Spacing.tiny
        detailStackView.alignment = .leading
        detailStackView.distribution = .fill
        
        // Add subviews
        contentView.addSubview(containerView)
        containerView.addSubviews(gradientView, detailStackView, quantityLabel, priceLabel)
        
        topStackView.addArrangedSubview(statusIndicatorView)
        topStackView.addArrangedSubview(leatherNameLabel)
        topStackView.addArrangedSubview(statusLabel)
        
        bottomStackView.addArrangedSubview(supplierLabel)
        bottomStackView.addArrangedSubview(UIView()) // Spacer
        bottomStackView.addArrangedSubview(purchaseDateLabel)
        
        detailStackView.addArrangedSubview(topStackView)
        detailStackView.addArrangedSubview(bottomStackView)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(Constants.Spacing.small)
        }
        
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        statusIndicatorView.snp.makeConstraints { make in
            make.width.height.equalTo(8)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.width.greaterThanOrEqualTo(60)
            make.height.equalTo(20)
        }
        
        detailStackView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(Constants.Spacing.medium)
            make.top.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalTo(quantityLabel.snp.leading).offset(-Constants.Spacing.small)
        }
        
        quantityLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.greaterThanOrEqualTo(80)
        }
        
        priceLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.greaterThanOrEqualTo(80)
        }
        
        topStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }
        
        bottomStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    func configure(with stockEntry: StockEntry) {
        leatherNameLabel.text = stockEntry.leatherName
        quantityLabel.text = stockEntry.formattedQuantity
        supplierLabel.text = stockEntry.supplier.isEmpty ? "Unknown Supplier" : stockEntry.supplier
        purchaseDateLabel.text = stockEntry.formattedPurchaseDate
        priceLabel.text = stockEntry.formattedTotalCost
        
        // Configure status
        statusLabel.text = stockEntry.stockStatus
        configureStatusAppearance(for: stockEntry.stockStatus)
        
        // Animate appearance
        containerView.scaleIn()
    }
    
    private func configureStatusAppearance(for status: String) {
        switch status {
        case "In Stock":
            statusLabel.backgroundColor = .appSuccess
            statusIndicatorView.backgroundColor = .appSuccess
        case "Low Stock":
            statusLabel.backgroundColor = .appWarning
            statusIndicatorView.backgroundColor = .appWarning
        case "Out of Stock":
            statusLabel.backgroundColor = .appError
            statusIndicatorView.backgroundColor = .appError
        case "On Order":
            statusLabel.backgroundColor = .appAccent
            statusIndicatorView.backgroundColor = .appAccent
        default:
            statusLabel.backgroundColor = .systemGray
            statusIndicatorView.backgroundColor = .systemGray
        }
    }
}
