//
//  AddLeatherViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class AddLeatherViewController: UIViewController {

    // MARK: - Properties
    private let dataManager = LeatherDataManager.shared
    private var leather: LeatherModel?
    private var isEditMode: Bool {
        return leather != nil
    }

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()

    // Form components
    private let nameTextField = UITextField()
    private let materialSourceButton = UIButton(type: .system)
    private let tanningMethodButton = UIButton(type: .system)
    private let surfaceTreatmentButton = UIButton(type: .system)
    private let thicknessTextField = UITextField()
    private let colorCodeTextField = UITextField()
    private let colorDescriptionTextField = UITextField()
    private let originTextField = UITextField()
    private let supplierTextField = UITextField()
    private let brandTextField = UITextField()
    private let textureDescriptionButton = UIButton(type: .system)
    private let usageSuggestionTextView = UITextView()
    private let notesTextView = UITextView()
    private let priceTextField = UITextField()
    private let priceUnitTextField = UITextField()
    private let imageView = UIImageView()
    private let imageButton = UIButton(type: .system)

    // Form sections
    private let basicInfoSection = UIView()
    private let detailInfoSection = UIView()
    private let additionalInfoSection = UIView()

    // MARK: - Initialization
    init(leather: LeatherModel? = nil) {
        self.leather = leather
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        configureForm()

        if let leather = leather {
            populateForm(with: leather)
        }
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Scroll View
        scrollView.showsVerticalScrollIndicator = false
        scrollView.keyboardDismissMode = .onDrag

        // Content View
        contentView.backgroundColor = .clear

        // Setup form sections
        setupBasicInfoSection()
        setupDetailInfoSection()
        setupAdditionalInfoSection()

        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubviews(basicInfoSection, detailInfoSection, additionalInfoSection)
    }

    private func setupBasicInfoSection() {
        basicInfoSection.backgroundColor = .appCardBackground
        basicInfoSection.setCornerRadius(Constants.CornerRadius.medium)
        basicInfoSection.applyCardShadow()

        let titleLabel = createSectionTitleLabel("基本信息")

        // Name
        let nameLabel = createFieldLabel("皮革名称 *")
        nameTextField.placeholder = "请输入皮革名称"
        styleTextField(nameTextField)

        // Material Source
        let materialLabel = createFieldLabel("材质来源 *")
        materialSourceButton.setTitle("请选择材质来源", for: .normal)
        styleSelectionButton(materialSourceButton)
        materialSourceButton.addTarget(self, action: #selector(materialSourceButtonTapped), for: .touchUpInside)

        // Tanning Method
        let tanningLabel = createFieldLabel("鞣制方式 *")
        tanningMethodButton.setTitle("请选择鞣制方式", for: .normal)
        styleSelectionButton(tanningMethodButton)
        tanningMethodButton.addTarget(self, action: #selector(tanningMethodButtonTapped), for: .touchUpInside)

        // Surface Treatment
        let surfaceLabel = createFieldLabel("表面处理 *")
        surfaceTreatmentButton.setTitle("请选择表面处理", for: .normal)
        styleSelectionButton(surfaceTreatmentButton)
        surfaceTreatmentButton.addTarget(self, action: #selector(surfaceTreatmentButtonTapped), for: .touchUpInside)

        // Thickness
        let thicknessLabel = createFieldLabel("厚度 (mm) *")
        thicknessTextField.placeholder = "0.0"
        thicknessTextField.keyboardType = .decimalPad
        styleTextField(thicknessTextField)

        basicInfoSection.addSubviews(
            titleLabel, nameLabel, nameTextField,
            materialLabel, materialSourceButton,
            tanningLabel, tanningMethodButton,
            surfaceLabel, surfaceTreatmentButton,
            thicknessLabel, thicknessTextField
        )

        // Constraints for basic info section
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        nameTextField.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        materialLabel.snp.makeConstraints { make in
            make.top.equalTo(nameTextField.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        materialSourceButton.snp.makeConstraints { make in
            make.top.equalTo(materialLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        tanningLabel.snp.makeConstraints { make in
            make.top.equalTo(materialSourceButton.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        tanningMethodButton.snp.makeConstraints { make in
            make.top.equalTo(tanningLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        surfaceLabel.snp.makeConstraints { make in
            make.top.equalTo(tanningMethodButton.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        surfaceTreatmentButton.snp.makeConstraints { make in
            make.top.equalTo(surfaceLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        thicknessLabel.snp.makeConstraints { make in
            make.top.equalTo(surfaceTreatmentButton.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        thicknessTextField.snp.makeConstraints { make in
            make.top.equalTo(thicknessLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func setupDetailInfoSection() {
        detailInfoSection.backgroundColor = .appCardBackground
        detailInfoSection.setCornerRadius(Constants.CornerRadius.medium)
        detailInfoSection.applyCardShadow()

        let titleLabel = createSectionTitleLabel("详细信息")

        // Color Code
        let colorCodeLabel = createFieldLabel("色号")
        colorCodeTextField.placeholder = "如：#8B4513"
        styleTextField(colorCodeTextField)

        // Color Description
        let colorDescLabel = createFieldLabel("颜色描述")
        colorDescriptionTextField.placeholder = "如：自然棕色"
        styleTextField(colorDescriptionTextField)

        // Origin
        let originLabel = createFieldLabel("产地")
        originTextField.placeholder = "如：意大利"
        styleTextField(originTextField)

        // Supplier
        let supplierLabel = createFieldLabel("供应商")
        supplierTextField.placeholder = "如：Conceria Santa Croce"
        styleTextField(supplierTextField)

        // Brand
        let brandLabel = createFieldLabel("品牌")
        brandTextField.placeholder = "如：Buttero"
        styleTextField(brandTextField)

        // Texture Description
        let textureLabel = createFieldLabel("手感描述")
        textureDescriptionButton.setTitle("请选择手感描述", for: .normal)
        styleSelectionButton(textureDescriptionButton)
        textureDescriptionButton.addTarget(self, action: #selector(textureDescriptionButtonTapped), for: .touchUpInside)

        detailInfoSection.addSubviews(
            titleLabel, colorCodeLabel, colorCodeTextField,
            colorDescLabel, colorDescriptionTextField,
            originLabel, originTextField,
            supplierLabel, supplierTextField,
            brandLabel, brandTextField,
            textureLabel, textureDescriptionButton
        )

        // Constraints for detail info section
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        colorCodeLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        colorCodeTextField.snp.makeConstraints { make in
            make.top.equalTo(colorCodeLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        colorDescLabel.snp.makeConstraints { make in
            make.top.equalTo(colorCodeTextField.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        colorDescriptionTextField.snp.makeConstraints { make in
            make.top.equalTo(colorDescLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        originLabel.snp.makeConstraints { make in
            make.top.equalTo(colorDescriptionTextField.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        originTextField.snp.makeConstraints { make in
            make.top.equalTo(originLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        supplierLabel.snp.makeConstraints { make in
            make.top.equalTo(originTextField.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        supplierTextField.snp.makeConstraints { make in
            make.top.equalTo(supplierLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        brandLabel.snp.makeConstraints { make in
            make.top.equalTo(supplierTextField.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        brandTextField.snp.makeConstraints { make in
            make.top.equalTo(brandLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        textureLabel.snp.makeConstraints { make in
            make.top.equalTo(brandTextField.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        textureDescriptionButton.snp.makeConstraints { make in
            make.top.equalTo(textureLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func setupAdditionalInfoSection() {
        additionalInfoSection.backgroundColor = .appCardBackground
        additionalInfoSection.setCornerRadius(Constants.CornerRadius.medium)
        additionalInfoSection.applyCardShadow()

        let titleLabel = createSectionTitleLabel("附加信息")

        // Usage Suggestion
        let usageLabel = createFieldLabel("使用建议")
        usageSuggestionTextView.font = Constants.Fonts.body
        usageSuggestionTextView.textColor = .appTextPrimary
        usageSuggestionTextView.backgroundColor = .systemGray6
        usageSuggestionTextView.setCornerRadius(Constants.CornerRadius.small)
        usageSuggestionTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)

        // Notes
        let notesLabel = createFieldLabel("备注")
        notesTextView.font = Constants.Fonts.body
        notesTextView.textColor = .appTextPrimary
        notesTextView.backgroundColor = .systemGray6
        notesTextView.setCornerRadius(Constants.CornerRadius.small)
        notesTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)

        // Price
        let priceLabel = createFieldLabel("价格")
        let priceStackView = UIStackView()
        priceStackView.axis = .horizontal
        priceStackView.spacing = Constants.Spacing.small
        priceStackView.distribution = .fill

        priceTextField.placeholder = "0.00"
        priceTextField.keyboardType = .decimalPad
        styleTextField(priceTextField)

        priceUnitTextField.placeholder = "元/平方尺"
        styleTextField(priceUnitTextField)

        priceStackView.addArrangedSubview(priceTextField)
        priceStackView.addArrangedSubview(priceUnitTextField)

        // Image
        let imageLabel = createFieldLabel("图片")
        imageView.contentMode = .scaleAspectFill
        imageView.backgroundColor = .systemGray6
        imageView.setCornerRadius(Constants.CornerRadius.small)
        imageView.image = UIImage(systemName: "photo")
        imageView.tintColor = .systemGray3

        imageButton.setTitle("选择图片", for: .normal)
        imageButton.setTitleColor(.appAccent, for: .normal)
        imageButton.titleLabel?.font = Constants.Fonts.callout
        imageButton.addTarget(self, action: #selector(imageButtonTapped), for: .touchUpInside)

        additionalInfoSection.addSubviews(
            titleLabel, usageLabel, usageSuggestionTextView,
            notesLabel, notesTextView,
            priceLabel, priceStackView,
            imageLabel, imageView, imageButton
        )

        // Constraints for additional info section
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        usageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        usageSuggestionTextView.snp.makeConstraints { make in
            make.top.equalTo(usageLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(80)
        }

        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(usageSuggestionTextView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        notesTextView.snp.makeConstraints { make in
            make.top.equalTo(notesLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(80)
        }

        priceLabel.snp.makeConstraints { make in
            make.top.equalTo(notesTextView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        priceStackView.snp.makeConstraints { make in
            make.top.equalTo(priceLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(44)
        }

        priceTextField.snp.makeConstraints { make in
            make.width.equalTo(priceUnitTextField).multipliedBy(1.5)
        }

        imageLabel.snp.makeConstraints { make in
            make.top.equalTo(priceStackView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        imageView.snp.makeConstraints { make in
            make.top.equalTo(imageLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(120)
        }

        imageButton.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(Constants.Spacing.small)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        basicInfoSection.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        detailInfoSection.snp.makeConstraints { make in
            make.top.equalTo(basicInfoSection.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        additionalInfoSection.snp.makeConstraints { make in
            make.top.equalTo(detailInfoSection.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.large)
        }
    }

    private func setupNavigationBar() {
        title = isEditMode ? "编辑皮革" : "添加皮革"

        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )
    }

    private func configureForm() {
        // Add toolbar to numeric text fields
        let toolbar = UIToolbar()
        toolbar.sizeToFit()
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(dismissKeyboard))
        toolbar.setItems([UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil), doneButton], animated: false)

        thicknessTextField.inputAccessoryView = toolbar
        priceTextField.inputAccessoryView = toolbar
    }

    // MARK: - Helper Methods
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = Constants.Fonts.title3
        label.textColor = .appTextPrimary
        return label
    }

    private func createFieldLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = Constants.Fonts.callout
        label.textColor = .appTextSecondary
        return label
    }

    private func styleTextField(_ textField: UITextField) {
        textField.font = Constants.Fonts.body
        textField.textColor = .appTextPrimary
        textField.backgroundColor = .systemGray6
        textField.setCornerRadius(Constants.CornerRadius.small)
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.rightViewMode = .always
    }

    private func styleSelectionButton(_ button: UIButton) {
        button.titleLabel?.font = Constants.Fonts.body
        button.setTitleColor(.appTextPrimary, for: .normal)
        button.backgroundColor = .systemGray6
        button.setCornerRadius(Constants.CornerRadius.small)
        button.contentHorizontalAlignment = .left
        button.contentEdgeInsets = UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12)

        let chevronImage = UIImage(systemName: "chevron.down")
        button.setImage(chevronImage, for: .normal)
        button.tintColor = .appTextTertiary
        button.semanticContentAttribute = .forceRightToLeft
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: -8)
    }

    // MARK: - Form Population
    private func populateForm(with leather: LeatherModel) {
        nameTextField.text = leather.name
        materialSourceButton.setTitle(leather.materialSource, for: .normal)
        tanningMethodButton.setTitle(leather.tanningMethod, for: .normal)
        surfaceTreatmentButton.setTitle(leather.surfaceTreatment, for: .normal)
        thicknessTextField.text = String(leather.thickness)
        colorCodeTextField.text = leather.colorCode
        colorDescriptionTextField.text = leather.colorDescription
        originTextField.text = leather.origin
        supplierTextField.text = leather.supplier
        brandTextField.text = leather.brand
        textureDescriptionButton.setTitle(leather.textureDescription.isEmpty ? "请选择手感描述" : leather.textureDescription, for: .normal)
        usageSuggestionTextView.text = leather.usageSuggestion
        notesTextView.text = leather.notes

        if let price = leather.price {
            priceTextField.text = String(price)
        }
        priceUnitTextField.text = leather.priceUnit

        if let imageData = leather.imageData, let image = UIImage(data: imageData) {
            imageView.image = image
            imageView.contentMode = .scaleAspectFill
        }
    }

    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    @objc private func saveButtonTapped() {
        guard validateForm() else { return }

        let leatherData = createLeatherFromForm()

        if isEditMode {
            dataManager.updateLeather(leatherData)
        } else {
            dataManager.addLeather(leatherData)
        }

        dismiss(animated: true)
    }

    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }

    @objc private func materialSourceButtonTapped() {
        showSelectionAlert(
            title: "选择材质来源",
            options: Constants.Leather.materialSources,
            selectedOption: materialSourceButton.title(for: .normal),
            button: materialSourceButton
        )
    }

    @objc private func tanningMethodButtonTapped() {
        showSelectionAlert(
            title: "选择鞣制方式",
            options: Constants.Leather.tanningMethods,
            selectedOption: tanningMethodButton.title(for: .normal),
            button: tanningMethodButton
        )
    }

    @objc private func surfaceTreatmentButtonTapped() {
        showSelectionAlert(
            title: "选择表面处理",
            options: Constants.Leather.surfaceTreatments,
            selectedOption: surfaceTreatmentButton.title(for: .normal),
            button: surfaceTreatmentButton
        )
    }

    @objc private func textureDescriptionButtonTapped() {
        showSelectionAlert(
            title: "选择手感描述",
            options: Constants.Leather.textureDescriptions,
            selectedOption: textureDescriptionButton.title(for: .normal),
            button: textureDescriptionButton
        )
    }

    @objc private func imageButtonTapped() {
        let alertController = UIAlertController(title: "选择图片", message: nil, preferredStyle: .actionSheet)

        alertController.addAction(UIAlertAction(title: "拍照", style: .default) { _ in
            self.presentImagePicker(sourceType: .camera)
        })

        alertController.addAction(UIAlertAction(title: "从相册选择", style: .default) { _ in
            self.presentImagePicker(sourceType: .photoLibrary)
        })

        if imageView.image != UIImage(systemName: "photo") {
            alertController.addAction(UIAlertAction(title: "删除图片", style: .destructive) { _ in
                self.imageView.image = UIImage(systemName: "photo")
                self.imageView.contentMode = .center
            })
        }

        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.sourceView = imageButton
            popover.sourceRect = imageButton.bounds
        }

        present(alertController, animated: true)
    }

    // MARK: - Helper Methods
    private func showSelectionAlert(title: String, options: [String], selectedOption: String?, button: UIButton) {
        let alertController = UIAlertController(title: title, message: nil, preferredStyle: .actionSheet)

        for option in options {
            let action = UIAlertAction(title: option, style: .default) { _ in
                button.setTitle(option, for: .normal)
            }

            if option == selectedOption {
                action.setValue(true, forKey: "checked")
            }

            alertController.addAction(action)
        }

        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.sourceView = button
            popover.sourceRect = button.bounds
        }

        present(alertController, animated: true)
    }

    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        guard UIImagePickerController.isSourceTypeAvailable(sourceType) else {
            showAlert(title: "错误", message: "该功能不可用")
            return
        }

        let imagePicker = UIImagePickerController()
        imagePicker.sourceType = sourceType
        imagePicker.delegate = self
        imagePicker.allowsEditing = true

        present(imagePicker, animated: true)
    }

    private func validateForm() -> Bool {
        let name = nameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let materialSource = materialSourceButton.title(for: .normal) ?? ""
        let tanningMethod = tanningMethodButton.title(for: .normal) ?? ""
        let surfaceTreatment = surfaceTreatmentButton.title(for: .normal) ?? ""
        let thicknessText = thicknessTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        var errors: [String] = []

        if name.isEmpty {
            errors.append("请输入皮革名称")
        }

        if materialSource.isEmpty || materialSource.hasPrefix("请选择") {
            errors.append("请选择材质来源")
        }

        if tanningMethod.isEmpty || tanningMethod.hasPrefix("请选择") {
            errors.append("请选择鞣制方式")
        }

        if surfaceTreatment.isEmpty || surfaceTreatment.hasPrefix("请选择") {
            errors.append("请选择表面处理")
        }

        if thicknessText.isEmpty {
            errors.append("请输入厚度")
        } else if let thickness = Double(thicknessText) {
            if thickness <= 0 {
                errors.append("厚度必须大于0")
            } else if thickness > Constants.Leather.thicknessRange.max {
                errors.append("厚度不能超过\(Constants.Leather.thicknessRange.max)mm")
            }
        } else {
            errors.append("厚度格式不正确")
        }

        if !errors.isEmpty {
            showAlert(title: "表单验证失败", message: errors.joined(separator: "\n"))
            return false
        }

        return true
    }

    private func createLeatherFromForm() -> LeatherModel {
        let name = nameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let materialSource = materialSourceButton.title(for: .normal) ?? ""
        let tanningMethod = tanningMethodButton.title(for: .normal) ?? ""
        let surfaceTreatment = surfaceTreatmentButton.title(for: .normal) ?? ""
        let thickness = Double(thicknessTextField.text ?? "0") ?? 0
        let colorCode = colorCodeTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let colorDescription = colorDescriptionTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let origin = originTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let supplier = supplierTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let brand = brandTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let textureDescription = textureDescriptionButton.title(for: .normal)?.hasPrefix("请选择") == true ? "" : (textureDescriptionButton.title(for: .normal) ?? "")
        let usageSuggestion = usageSuggestionTextView.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let notes = notesTextView.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let price = Double(priceTextField.text ?? "")
        let priceUnit = priceUnitTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines)

        var imageData: Data?
        if let image = imageView.image, image != UIImage(systemName: "photo") {
            imageData = image.jpegData(compressionQuality: 0.8)
        }

        if isEditMode, let existingLeather = leather {
            var updatedLeather = existingLeather
            updatedLeather.name = name
            updatedLeather.materialSource = materialSource
            updatedLeather.tanningMethod = tanningMethod
            updatedLeather.surfaceTreatment = surfaceTreatment
            updatedLeather.thickness = thickness
            updatedLeather.colorCode = colorCode
            updatedLeather.colorDescription = colorDescription
            updatedLeather.origin = origin
            updatedLeather.supplier = supplier
            updatedLeather.brand = brand
            updatedLeather.textureDescription = textureDescription
            updatedLeather.usageSuggestion = usageSuggestion
            updatedLeather.notes = notes
            updatedLeather.price = price
            updatedLeather.priceUnit = priceUnit
            updatedLeather.imageData = imageData
            updatedLeather.updateTimestamp()
            return updatedLeather
        } else {
            return LeatherModel(
                name: name,
                materialSource: materialSource,
                tanningMethod: tanningMethod,
                surfaceTreatment: surfaceTreatment,
                thickness: thickness,
                colorCode: colorCode,
                colorDescription: colorDescription,
                origin: origin,
                supplier: supplier,
                brand: brand,
                textureDescription: textureDescription,
                usageSuggestion: usageSuggestion,
                imageData: imageData,
                notes: notes,
                price: price,
                priceUnit: priceUnit
            )
        }
    }

    private func showAlert(title: String, message: String) {
        let alertController = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "确定", style: .default))
        present(alertController, animated: true)
    }
}

// MARK: - UIImagePickerControllerDelegate & UINavigationControllerDelegate
extension AddLeatherViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {

    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        if let editedImage = info[.editedImage] as? UIImage {
            imageView.image = editedImage
            imageView.contentMode = .scaleAspectFill
        } else if let originalImage = info[.originalImage] as? UIImage {
            imageView.image = originalImage
            imageView.contentMode = .scaleAspectFill
        }

        picker.dismiss(animated: true)
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}
