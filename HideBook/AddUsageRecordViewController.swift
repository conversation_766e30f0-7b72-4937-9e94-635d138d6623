//
//  AddUsageRecordViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class AddUsageRecordViewController: UIViewController {

    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    private var stockEntry: StockEntry?
    private var usageRecord: UsageRecord?
    private var isEditMode: Bool {
        return usageRecord != nil
    }

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()

    // Form components
    private let stockButton = UIButton(type: .system)
    private let projectNameTextField = UITextField()
    private let categoryButton = UIButton(type: .system)
    private let quantityUsedTextField = UITextField()
    private let currentStockLabel = UILabel()
    private let remainingQuantityLabel = UILabel()
    private let usageDatePicker = UIDatePicker()
    private let notesTextView = UITextView()

    private var selectedStock: StockEntry?

    // MARK: - Initialization
    init(stockEntry: StockEntry? = nil, usageRecord: UsageRecord? = nil) {
        self.stockEntry = stockEntry
        self.usageRecord = usageRecord
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        configureForm()

        if let stockEntry = stockEntry {
            selectedStock = stockEntry
            stockButton.setTitle(stockEntry.leatherName, for: .normal)
            updateStockInfo()
        }

        if let usageRecord = usageRecord {
            populateForm(with: usageRecord)
        }
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setSecondaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Scroll View
        scrollView.showsVerticalScrollIndicator = false
        scrollView.keyboardDismissMode = .onDrag

        // Content View
        contentView.backgroundColor = .clear

        setupFormComponents()

        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubviews(
            createSectionView(title: "Stock Selection", components: [
                createFieldLabel("Stock Entry *"),
                stockButton,
                createFieldLabel("Current Stock"),
                currentStockLabel
            ]),
            createSectionView(title: "Usage Information", components: [
                createFieldLabel("Project Name *"),
                projectNameTextField,
                createFieldLabel("Category"),
                categoryButton,
                createFieldLabel("Quantity Used *"),
                quantityUsedTextField,
                createFieldLabel("Remaining After Use"),
                remainingQuantityLabel,
                createFieldLabel("Usage Date"),
                usageDatePicker
            ]),
            createSectionView(title: "Notes", components: [
                createFieldLabel("Additional Notes"),
                notesTextView
            ])
        )
    }

    private func setupFormComponents() {
        // Stock Button
        stockButton.setTitle("Select Stock Entry", for: .normal)
        styleSelectionButton(stockButton)
        stockButton.addTarget(self, action: #selector(stockButtonTapped), for: .touchUpInside)

        // Current Stock Label
        currentStockLabel.font = Constants.Fonts.body
        currentStockLabel.textColor = .appTextSecondary
        currentStockLabel.text = "No stock selected"

        // Project Name TextField
        projectNameTextField.placeholder = "e.g., Bifold Wallet"
        styleTextField(projectNameTextField)

        // Category Button
        categoryButton.setTitle("Accessory", for: .normal)
        styleSelectionButton(categoryButton)
        categoryButton.addTarget(self, action: #selector(categoryButtonTapped), for: .touchUpInside)

        // Quantity Used TextField
        quantityUsedTextField.placeholder = "0.00"
        quantityUsedTextField.keyboardType = .decimalPad
        styleTextField(quantityUsedTextField)
        quantityUsedTextField.addTarget(self, action: #selector(quantityUsedChanged), for: .editingChanged)

        // Remaining Quantity Label
        remainingQuantityLabel.font = Constants.Fonts.body
        remainingQuantityLabel.textColor = .appAccent
        remainingQuantityLabel.text = "0.00"

        // Usage Date Picker
        usageDatePicker.datePickerMode = .date
        usageDatePicker.preferredDatePickerStyle = .compact
        usageDatePicker.maximumDate = Date()

        // Notes Text View
        notesTextView.font = Constants.Fonts.body
        notesTextView.textColor = .appTextPrimary
        notesTextView.backgroundColor = .systemGray6
        notesTextView.setCornerRadius(Constants.CornerRadius.small)
        notesTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // Setup constraints for sections
        let sections = contentView.subviews
        for (index, section) in sections.enumerated() {
            section.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)

                if index == 0 {
                    make.top.equalToSuperview().inset(Constants.Spacing.medium)
                } else {
                    make.top.equalTo(sections[index - 1].snp.bottom).offset(Constants.Spacing.medium)
                }

                if index == sections.count - 1 {
                    make.bottom.equalToSuperview().inset(Constants.Spacing.large)
                }
            }
        }

        notesTextView.snp.makeConstraints { make in
            make.height.equalTo(80)
        }
    }

    private func setupNavigationBar() {
        title = isEditMode ? "Edit Usage Record" : "Add Usage Record"

        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )
    }

    private func configureForm() {
        // Add toolbar to numeric text fields
        let toolbar = UIToolbar()
        toolbar.sizeToFit()
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(dismissKeyboard))
        toolbar.setItems([UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil), doneButton], animated: false)

        quantityUsedTextField.inputAccessoryView = toolbar
    }

    // MARK: - Helper Methods
    private func createSectionView(title: String, components: [UIView]) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = Constants.Spacing.small
        stackView.alignment = .fill
        stackView.distribution = .fill

        components.forEach { stackView.addArrangedSubview($0) }

        sectionView.addSubviews(titleLabel, stackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }

        return sectionView
    }

    private func createFieldLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = Constants.Fonts.callout
        label.textColor = .appTextSecondary
        return label
    }

    private func styleTextField(_ textField: UITextField) {
        textField.font = Constants.Fonts.body
        textField.textColor = .appTextPrimary
        textField.backgroundColor = .systemGray6
        textField.setCornerRadius(Constants.CornerRadius.small)
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.rightViewMode = .always

        textField.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }

    private func styleSelectionButton(_ button: UIButton) {
        button.titleLabel?.font = Constants.Fonts.body
        button.setTitleColor(.appTextPrimary, for: .normal)
        button.backgroundColor = .systemGray6
        button.setCornerRadius(Constants.CornerRadius.small)
        button.contentHorizontalAlignment = .left
        button.contentEdgeInsets = UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12)

        let chevronImage = UIImage(systemName: "chevron.down")
        button.setImage(chevronImage, for: .normal)
        button.tintColor = .appTextTertiary
        button.semanticContentAttribute = .forceRightToLeft
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: -8)

        button.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }

    // MARK: - Form Population
    private func populateForm(with usageRecord: UsageRecord) {
        selectedStock = stockDataManager.getStockEntry(withId: usageRecord.stockId)
        stockButton.setTitle(selectedStock?.leatherName ?? "Unknown Stock", for: .normal)
        projectNameTextField.text = usageRecord.projectName
        categoryButton.setTitle(usageRecord.category, for: .normal)
        quantityUsedTextField.text = String(usageRecord.quantityUsed)
        usageDatePicker.date = usageRecord.usageDate
        notesTextView.text = usageRecord.notes
        updateStockInfo()
    }

    private func updateStockInfo() {
        guard let stock = selectedStock else {
            currentStockLabel.text = "No stock selected"
            remainingQuantityLabel.text = "0.00"
            return
        }

        currentStockLabel.text = stock.formattedQuantity
        updateRemainingQuantity()
    }

    private func updateRemainingQuantity() {
        guard let stock = selectedStock else {
            remainingQuantityLabel.text = "0.00"
            return
        }

        let quantityUsed = Double(quantityUsedTextField.text ?? "0") ?? 0
        let remaining = max(0, stock.currentQuantity - quantityUsed)
        remainingQuantityLabel.text = String(format: "%.2f %@", remaining, stock.areaUnit)

        // Color code based on remaining quantity
        if remaining <= 0 {
            remainingQuantityLabel.textColor = .appError
        } else if remaining <= stock.lowStockThreshold {
            remainingQuantityLabel.textColor = .appWarning
        } else {
            remainingQuantityLabel.textColor = .appSuccess
        }
    }

    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    @objc private func saveButtonTapped() {
        guard validateForm() else { return }

        let usageData = createUsageRecordFromForm()

        if isEditMode {
            stockDataManager.updateUsageRecord(usageData)
        } else {
            stockDataManager.addUsageRecord(usageData)
        }

        dismiss(animated: true)
    }

    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }

    @objc private func stockButtonTapped() {
        let stockEntries = stockDataManager.allStockEntries.filter { $0.currentQuantity > 0 }
        guard !stockEntries.isEmpty else {
            showAlert(title: "No Stock Available", message: "Please add some stock entries first in the Stock tab.")
            return
        }

        let alertController = UIAlertController(title: "Select Stock Entry", message: nil, preferredStyle: .actionSheet)

        for stock in stockEntries {
            let title = "\(stock.leatherName) - \(stock.formattedQuantity)"
            let action = UIAlertAction(title: title, style: .default) { _ in
                self.selectedStock = stock
                self.stockButton.setTitle(stock.leatherName, for: .normal)
                self.updateStockInfo()
            }
            alertController.addAction(action)
        }

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.sourceView = stockButton
            popover.sourceRect = stockButton.bounds
        }

        present(alertController, animated: true)
    }

    @objc private func categoryButtonTapped() {
        let alertController = UIAlertController(title: "Select Category", message: nil, preferredStyle: .actionSheet)

        for category in Constants.Stock.usageCategories {
            let action = UIAlertAction(title: category, style: .default) { _ in
                self.categoryButton.setTitle(category, for: .normal)
            }

            if category == categoryButton.title(for: .normal) {
                action.setValue(true, forKey: "checked")
            }

            alertController.addAction(action)
        }

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.sourceView = categoryButton
            popover.sourceRect = categoryButton.bounds
        }

        present(alertController, animated: true)
    }

    @objc private func quantityUsedChanged() {
        updateRemainingQuantity()
    }

    // MARK: - Validation & Form Creation
    private func validateForm() -> Bool {
        var errors: [String] = []

        if selectedStock == nil {
            errors.append("Please select a stock entry")
        }

        let projectName = projectNameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        if projectName.isEmpty {
            errors.append("Please enter project name")
        }

        let quantityUsedText = quantityUsedTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        if quantityUsedText.isEmpty {
            errors.append("Please enter quantity used")
        } else if let quantityUsed = Double(quantityUsedText) {
            if quantityUsed <= 0 {
                errors.append("Quantity used must be greater than 0")
            } else if let stock = selectedStock, quantityUsed > stock.currentQuantity {
                errors.append("Quantity used cannot exceed current stock (\(stock.formattedQuantity))")
            }
        } else {
            errors.append("Invalid quantity used format")
        }

        if !errors.isEmpty {
            showAlert(title: "Form Validation Failed", message: errors.joined(separator: "\n"))
            return false
        }

        return true
    }

    private func createUsageRecordFromForm() -> UsageRecord {
        let projectName = projectNameTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let category = categoryButton.title(for: .normal) ?? "Accessory"
        let quantityUsed = Double(quantityUsedTextField.text ?? "0") ?? 0
        let remainingAfterUse = max(0, (selectedStock?.currentQuantity ?? 0) - quantityUsed)
        let notes = notesTextView.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        if isEditMode, let existingRecord = usageRecord {
            var updatedRecord = existingRecord
            updatedRecord.projectName = projectName
            updatedRecord.category = category
            updatedRecord.quantityUsed = quantityUsed
            updatedRecord.remainingAfterUse = remainingAfterUse
            updatedRecord.usageDate = usageDatePicker.date
            updatedRecord.notes = notes
            return updatedRecord
        } else {
            return UsageRecord(
                stockId: selectedStock?.id ?? "",
                projectName: projectName,
                category: category,
                quantityUsed: quantityUsed,
                remainingAfterUse: remainingAfterUse,
                usageDate: usageDatePicker.date,
                notes: notes
            )
        }
    }

    private func showAlert(title: String, message: String) {
        let alertController = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "OK", style: .default))
        present(alertController, animated: true)
    }
}
