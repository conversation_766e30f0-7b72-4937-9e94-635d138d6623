//
//  AddStockEntryViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class AddStockEntryViewController: UIViewController {

    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    private let leatherDataManager = LeatherDataManager.shared
    private var stockEntry: StockEntry?
    private var isEditMode: Bool {
        return stockEntry != nil
    }

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()

    // Form components
    private let leatherButton = UIButton(type: .system)
    private let quantityTextField = UITextField()
    private let areaUnitButton = UIButton(type: .system)
    private let unitPriceTextField = UITextField()
    private let currencyButton = UIButton(type: .system)
    private let supplierTextField = UITextField()
    private let batchNumberTextField = UITextField()
    private let purchaseDatePicker = UIDatePicker()
    private let lowStockThresholdTextField = UITextField()
    private let notesTextView = UITextView()

    private var selectedLeather: LeatherModel?

    // MARK: - Initialization
    init(stockEntry: StockEntry? = nil) {
        self.stockEntry = stockEntry
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        configureForm()

        if let stockEntry = stockEntry {
            populateForm(with: stockEntry)
        }
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Scroll View
        scrollView.showsVerticalScrollIndicator = false
        scrollView.keyboardDismissMode = .onDrag

        // Content View
        contentView.backgroundColor = .clear

        setupFormComponents()

        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubviews(
            createSectionView(title: "Leather Selection", components: [
                createFieldLabel("Leather *"),
                leatherButton
            ]),
            createSectionView(title: "Stock Information", components: [
                createFieldLabel("Quantity *"),
                createHorizontalStack([quantityTextField, areaUnitButton]),
                createFieldLabel("Unit Price *"),
                createHorizontalStack([unitPriceTextField, currencyButton]),
                createFieldLabel("Supplier"),
                supplierTextField,
                createFieldLabel("Batch Number"),
                batchNumberTextField,
                createFieldLabel("Purchase Date"),
                purchaseDatePicker,
                createFieldLabel("Low Stock Threshold"),
                lowStockThresholdTextField
            ]),
            createSectionView(title: "Notes", components: [
                createFieldLabel("Additional Notes"),
                notesTextView
            ])
        )
    }

    private func setupFormComponents() {
        // Leather Button
        leatherButton.setTitle("Select Leather", for: .normal)
        styleSelectionButton(leatherButton)
        leatherButton.addTarget(self, action: #selector(leatherButtonTapped), for: .touchUpInside)

        // Quantity TextField
        quantityTextField.placeholder = "0.00"
        quantityTextField.keyboardType = .decimalPad
        styleTextField(quantityTextField)

        // Area Unit Button
        areaUnitButton.setTitle("sq ft", for: .normal)
        styleSelectionButton(areaUnitButton)
        areaUnitButton.addTarget(self, action: #selector(areaUnitButtonTapped), for: .touchUpInside)

        // Unit Price TextField
        unitPriceTextField.placeholder = "0.00"
        unitPriceTextField.keyboardType = .decimalPad
        styleTextField(unitPriceTextField)

        // Currency Button
        currencyButton.setTitle("$", for: .normal)
        styleSelectionButton(currencyButton)
        currencyButton.addTarget(self, action: #selector(currencyButtonTapped), for: .touchUpInside)

        // Supplier TextField
        supplierTextField.placeholder = "Supplier name"
        styleTextField(supplierTextField)

        // Batch Number TextField
        batchNumberTextField.placeholder = "Batch identifier"
        styleTextField(batchNumberTextField)

        // Purchase Date Picker
        purchaseDatePicker.datePickerMode = .date
        purchaseDatePicker.preferredDatePickerStyle = .compact
        purchaseDatePicker.maximumDate = Date()

        // Low Stock Threshold TextField
        lowStockThresholdTextField.placeholder = "10.0"
        lowStockThresholdTextField.keyboardType = .decimalPad
        styleTextField(lowStockThresholdTextField)

        // Notes Text View
        notesTextView.font = Constants.Fonts.body
        notesTextView.textColor = .appTextPrimary
        notesTextView.backgroundColor = .systemGray6
        notesTextView.setCornerRadius(Constants.CornerRadius.small)
        notesTextView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // Setup constraints for sections
        let sections = contentView.subviews
        for (index, section) in sections.enumerated() {
            section.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)

                if index == 0 {
                    make.top.equalToSuperview().inset(Constants.Spacing.medium)
                } else {
                    make.top.equalTo(sections[index - 1].snp.bottom).offset(Constants.Spacing.medium)
                }

                if index == sections.count - 1 {
                    make.bottom.equalToSuperview().inset(Constants.Spacing.large)
                }
            }
        }

        notesTextView.snp.makeConstraints { make in
            make.height.equalTo(80)
        }
    }

    private func setupNavigationBar() {
        title = isEditMode ? "Edit Stock Entry" : "Add Stock Entry"

        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        navigationItem.rightBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .save,
            target: self,
            action: #selector(saveButtonTapped)
        )
    }

    private func configureForm() {
        // Add toolbar to numeric text fields
        let toolbar = UIToolbar()
        toolbar.sizeToFit()
        let doneButton = UIBarButtonItem(barButtonSystemItem: .done, target: self, action: #selector(dismissKeyboard))
        toolbar.setItems([UIBarButtonItem(barButtonSystemItem: .flexibleSpace, target: nil, action: nil), doneButton], animated: false)

        quantityTextField.inputAccessoryView = toolbar
        unitPriceTextField.inputAccessoryView = toolbar
        lowStockThresholdTextField.inputAccessoryView = toolbar
    }

    // MARK: - Helper Methods
    private func createSectionView(title: String, components: [UIView]) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = Constants.Spacing.small
        stackView.alignment = .fill
        stackView.distribution = .fill

        components.forEach { stackView.addArrangedSubview($0) }

        sectionView.addSubviews(titleLabel, stackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }

        return sectionView
    }

    private func createFieldLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = Constants.Fonts.callout
        label.textColor = .appTextSecondary
        return label
    }

    private func createHorizontalStack(_ views: [UIView]) -> UIStackView {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = Constants.Spacing.small
        stackView.distribution = .fill

        views.forEach { stackView.addArrangedSubview($0) }

        // Make first view take more space
        if views.count == 2 {
            views[0].snp.makeConstraints { make in
                make.width.equalTo(views[1]).multipliedBy(2)
            }
        }

        return stackView
    }

    private func styleTextField(_ textField: UITextField) {
        textField.font = Constants.Fonts.body
        textField.textColor = .appTextPrimary
        textField.backgroundColor = .systemGray6
        textField.setCornerRadius(Constants.CornerRadius.small)
        textField.leftView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.leftViewMode = .always
        textField.rightView = UIView(frame: CGRect(x: 0, y: 0, width: 12, height: 0))
        textField.rightViewMode = .always

        textField.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }

    private func styleSelectionButton(_ button: UIButton) {
        button.titleLabel?.font = Constants.Fonts.body
        button.setTitleColor(.appTextPrimary, for: .normal)
        button.backgroundColor = .systemGray6
        button.setCornerRadius(Constants.CornerRadius.small)
        button.contentHorizontalAlignment = .left
        button.contentEdgeInsets = UIEdgeInsets(top: 0, left: 12, bottom: 0, right: 12)

        let chevronImage = UIImage(systemName: "chevron.down")
        button.setImage(chevronImage, for: .normal)
        button.tintColor = .appTextTertiary
        button.semanticContentAttribute = .forceRightToLeft
        button.imageEdgeInsets = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: -8)

        button.snp.makeConstraints { make in
            make.height.equalTo(44)
        }
    }

    // MARK: - Form Population
    private func populateForm(with stockEntry: StockEntry) {
        selectedLeather = leatherDataManager.getLeather(withId: stockEntry.leatherId)
        leatherButton.setTitle(selectedLeather?.name ?? "Unknown Leather", for: .normal)
        quantityTextField.text = String(stockEntry.currentQuantity)
        areaUnitButton.setTitle(stockEntry.areaUnit, for: .normal)
        unitPriceTextField.text = String(stockEntry.unitPrice)
        currencyButton.setTitle(stockEntry.currency, for: .normal)
        supplierTextField.text = stockEntry.supplier
        batchNumberTextField.text = stockEntry.batchNumber
        purchaseDatePicker.date = stockEntry.purchaseDate
        lowStockThresholdTextField.text = String(stockEntry.lowStockThreshold)
        notesTextView.text = stockEntry.notes
    }

    // MARK: - Actions
    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }

    @objc private func saveButtonTapped() {
        guard validateForm() else { return }

        let stockData = createStockEntryFromForm()

        if isEditMode {
            stockDataManager.updateStockEntry(stockData)
        } else {
            stockDataManager.addStockEntry(stockData)
        }

        dismiss(animated: true)
    }

    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }

    @objc private func leatherButtonTapped() {
        let leathers = leatherDataManager.allLeathers
        guard !leathers.isEmpty else {
            showAlert(title: "No Leathers", message: "Please add some leather entries first in the Archive tab.")
            return
        }

        let alertController = UIAlertController(title: "Select Leather", message: nil, preferredStyle: .actionSheet)

        for leather in leathers {
            let action = UIAlertAction(title: leather.displayName, style: .default) { _ in
                self.selectedLeather = leather
                self.leatherButton.setTitle(leather.displayName, for: .normal)
            }
            alertController.addAction(action)
        }

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.sourceView = leatherButton
            popover.sourceRect = leatherButton.bounds
        }

        present(alertController, animated: true)
    }

    @objc private func areaUnitButtonTapped() {
        showSelectionAlert(
            title: "Select Area Unit",
            options: Constants.Stock.areaUnits,
            selectedOption: areaUnitButton.title(for: .normal),
            button: areaUnitButton
        )
    }

    @objc private func currencyButtonTapped() {
        showSelectionAlert(
            title: "Select Currency",
            options: Constants.Stock.currencyUnits,
            selectedOption: currencyButton.title(for: .normal),
            button: currencyButton
        )
    }

    // MARK: - Helper Methods
    private func showSelectionAlert(title: String, options: [String], selectedOption: String?, button: UIButton) {
        let alertController = UIAlertController(title: title, message: nil, preferredStyle: .actionSheet)

        for option in options {
            let action = UIAlertAction(title: option, style: .default) { _ in
                button.setTitle(option, for: .normal)
            }

            if option == selectedOption {
                action.setValue(true, forKey: "checked")
            }

            alertController.addAction(action)
        }

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.sourceView = button
            popover.sourceRect = button.bounds
        }

        present(alertController, animated: true)
    }

    private func validateForm() -> Bool {
        var errors: [String] = []

        if selectedLeather == nil {
            errors.append("Please select a leather")
        }

        let quantityText = quantityTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        if quantityText.isEmpty {
            errors.append("Please enter quantity")
        } else if let quantity = Double(quantityText), quantity <= 0 {
            errors.append("Quantity must be greater than 0")
        } else if Double(quantityText) == nil {
            errors.append("Invalid quantity format")
        }

        let unitPriceText = unitPriceTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        if unitPriceText.isEmpty {
            errors.append("Please enter unit price")
        } else if let price = Double(unitPriceText), price < 0 {
            errors.append("Unit price cannot be negative")
        } else if Double(unitPriceText) == nil {
            errors.append("Invalid unit price format")
        }

        if !errors.isEmpty {
            showAlert(title: "Form Validation Failed", message: errors.joined(separator: "\n"))
            return false
        }

        return true
    }

    private func createStockEntryFromForm() -> StockEntry {
        let quantity = Double(quantityTextField.text ?? "0") ?? 0
        let unitPrice = Double(unitPriceTextField.text ?? "0") ?? 0
        let areaUnit = areaUnitButton.title(for: .normal) ?? "sq ft"
        let currency = currencyButton.title(for: .normal) ?? "$"
        let supplier = supplierTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let batchNumber = batchNumberTextField.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let lowStockThreshold = Double(lowStockThresholdTextField.text ?? "10") ?? 10.0
        let notes = notesTextView.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""

        if isEditMode, let existingEntry = stockEntry {
            var updatedEntry = existingEntry
            updatedEntry.currentQuantity = quantity
            updatedEntry.areaUnit = areaUnit
            updatedEntry.unitPrice = unitPrice
            updatedEntry.totalCost = unitPrice * quantity
            updatedEntry.currency = currency
            updatedEntry.supplier = supplier
            updatedEntry.batchNumber = batchNumber
            updatedEntry.purchaseDate = purchaseDatePicker.date
            updatedEntry.lowStockThreshold = lowStockThreshold
            updatedEntry.notes = notes
            updatedEntry.updateTimestamp()
            return updatedEntry
        } else {
            return StockEntry(
                leatherId: selectedLeather?.id ?? "",
                currentQuantity: quantity,
                areaUnit: areaUnit,
                unitPrice: unitPrice,
                currency: currency,
                supplier: supplier,
                batchNumber: batchNumber,
                purchaseDate: purchaseDatePicker.date,
                lowStockThreshold: lowStockThreshold,
                notes: notes
            )
        }
    }

    private func showAlert(title: String, message: String) {
        let alertController = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "OK", style: .default))
        present(alertController, animated: true)
    }
}
