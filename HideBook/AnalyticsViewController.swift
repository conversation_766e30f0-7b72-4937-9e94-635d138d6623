//
//  AnalyticsViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit
import AAInfographics

class AnalyticsViewController: UIViewController {
    
    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    private let leatherDataManager = LeatherDataManager.shared
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()
    
    // Chart Views
    private let inventoryValueChartView = AAChartView()
    private let leatherTypeDistributionChartView = AAChartView()
    private let costAnalysisChartView = AAChartView()
    private let efficiencyChartView = AAChartView()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        populateData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05
        
        // Scroll View
        scrollView.showsVerticalScrollIndicator = false
        
        // Content View
        contentView.backgroundColor = .clear
        
        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Analytics"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            image: UIImage(systemName: "square.and.arrow.up"),
            style: .plain,
            target: self,
            action: #selector(shareButtonTapped)
        )
    }
    
    private func populateData() {
        // Create analytics sections
        let sections = [
            createChartSection(title: "Inventory Value Trend", chartView: inventoryValueChartView),
            createChartSection(title: "Leather Type Distribution", chartView: leatherTypeDistributionChartView),
            createChartSection(title: "Cost Analysis", chartView: costAnalysisChartView),
            createChartSection(title: "Usage Efficiency", chartView: efficiencyChartView)
        ]
        
        sections.forEach { contentView.addSubview($0) }
        
        // Setup constraints for sections
        for (index, section) in sections.enumerated() {
            section.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
                
                if index == 0 {
                    make.top.equalToSuperview().inset(Constants.Spacing.medium)
                } else {
                    make.top.equalTo(sections[index - 1].snp.bottom).offset(Constants.Spacing.medium)
                }
                
                if index == sections.count - 1 {
                    make.bottom.equalToSuperview().inset(Constants.Spacing.large)
                }
            }
        }
        
        // Configure charts
        setupCharts()
    }
    
    private func createChartSection(title: String, chartView: AAChartView) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary
        
        chartView.backgroundColor = .clear
        chartView.setCornerRadius(Constants.CornerRadius.small)
        
        sectionView.addSubviews(titleLabel, chartView)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        chartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(300)
        }
        
        return sectionView
    }
    
    private func setupCharts() {
        setupInventoryValueChart()
        setupLeatherTypeDistributionChart()
        setupCostAnalysisChart()
        setupEfficiencyChart()
    }
    
    private func setupInventoryValueChart() {
        let stockEntries = stockDataManager.allStockEntries
        let last12Months = getLast12Months()
        
        let monthlyValues = last12Months.map { month in
            stockEntries.filter { Calendar.current.isDate($0.purchaseDate, equalTo: month, toGranularity: .month) }
                .reduce(0.0) { $0 + $1.totalCost }
        }
        
        let cumulativeValues = monthlyValues.reduce(into: [Double]()) { result, value in
            let lastValue = result.last ?? 0
            result.append(lastValue + value)
        }
        
        let monthNames = last12Months.map { month in
            let formatter = DateFormatter()
            formatter.dateFormat = "MMM"
            return formatter.string(from: month)
        }
        
        let chartModel = AAChartModel()
            .chartType(.areaspline)
            .title("Inventory Value Growth")
            .subtitle("Cumulative investment over time")
            .dataLabelsEnabled(false)
            .yAxisTitle("Value ($)")
            .categories(monthNames)
            .series([
                AASeriesElement()
                    .name("Cumulative Value")
                    .data(cumulativeValues)
                    .color("#007bff")
                    .fillOpacity(0.3)
            ])
        
        inventoryValueChartView.aa_drawChartWithChartModel(chartModel)
    }
    
    private func setupLeatherTypeDistributionChart() {
        let leathers = leatherDataManager.allLeathers
        let materialSources = leathers.map { $0.materialSource }
        let materialCounts = Dictionary(grouping: materialSources, by: { $0 })
            .mapValues { $0.count }
        
        let materialData = materialCounts.map { (material, count) in
            [material, count]
        }
        
        let chartModel = AAChartModel()
            .chartType(.pie)
            .title("Leather Type Distribution")
            .subtitle("By material source")
            .dataLabelsEnabled(true)
            .series([
                AASeriesElement()
                    .name("Material Types")
                    .data(materialData)
                    .colorByPoint(true)
            ])
            .colors(["#8B4513", "#D2691E", "#CD853F", "#DEB887", "#F4A460"])
        
        leatherTypeDistributionChartView.aa_drawChartWithChartModel(chartModel)
    }
    
    private func setupCostAnalysisChart() {
        let stockEntries = stockDataManager.allStockEntries
        let priceRanges = ["$0-25", "$25-50", "$50-100", "$100+"]
        
        let rangeCounts = priceRanges.map { range in
            switch range {
            case "$0-25":
                return stockEntries.filter { $0.unitPrice < 25 }.count
            case "$25-50":
                return stockEntries.filter { $0.unitPrice >= 25 && $0.unitPrice < 50 }.count
            case "$50-100":
                return stockEntries.filter { $0.unitPrice >= 50 && $0.unitPrice < 100 }.count
            case "$100+":
                return stockEntries.filter { $0.unitPrice >= 100 }.count
            default:
                return 0
            }
        }
        
        let chartModel = AAChartModel()
            .chartType(.column)
            .title("Cost Distribution")
            .subtitle("Stock entries by price range")
            .dataLabelsEnabled(true)
            .yAxisTitle("Number of Entries")
            .categories(priceRanges)
            .series([
                AASeriesElement()
                    .name("Entries")
                    .data(rangeCounts)
                    .color("#28a745")
            ])
        
        costAnalysisChartView.aa_drawChartWithChartModel(chartModel)
    }
    
    private func setupEfficiencyChart() {
        let stockEntries = stockDataManager.allStockEntries
        let usageRecords = stockDataManager.allUsageRecords
        
        let efficiencyData = stockEntries.compactMap { stock -> [Any]? in
            let totalUsed = usageRecords.filter { $0.stockId == stock.id }
                .reduce(0.0) { $0 + $1.quantityUsed }
            let efficiency = (totalUsed / (stock.currentQuantity + totalUsed)) * 100
            
            guard efficiency > 0 else { return nil }
            return [stock.leatherName, efficiency]
        }
        
        let chartModel = AAChartModel()
            .chartType(.scatter)
            .title("Usage Efficiency")
            .subtitle("Percentage of leather used")
            .dataLabelsEnabled(true)
            .yAxisTitle("Efficiency (%)")
            .series([
                AASeriesElement()
                    .name("Efficiency")
                    .data(efficiencyData)
                    .color("#17a2b8")
            ])
        
        efficiencyChartView.aa_drawChartWithChartModel(chartModel)
    }
    
    private func getLast12Months() -> [Date] {
        let calendar = Calendar.current
        let now = Date()
        var months: [Date] = []
        
        for i in 0..<12 {
            if let month = calendar.date(byAdding: .month, value: -i, to: now) {
                months.insert(month, at: 0)
            }
        }
        
        return months
    }
    
    // MARK: - Actions
    @objc private func doneButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func shareButtonTapped() {
        // TODO: Implement sharing functionality
        let alertController = UIAlertController(
            title: "Share Analytics",
            message: "Export functionality coming soon!",
            preferredStyle: .alert
        )
        alertController.addAction(UIAlertAction(title: "OK", style: .default))
        present(alertController, animated: true)
    }
}
