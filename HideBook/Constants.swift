//
//  Constants.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

struct Constants {

    // MARK: - Colors
    struct Colors {
        static let primaryGradientStart = UIColor(red: 0.2, green: 0.6, blue: 0.9, alpha: 1.0)
        static let primaryGradientEnd = UIColor(red: 0.1, green: 0.4, blue: 0.8, alpha: 1.0)

        static let secondaryGradientStart = UIColor(red: 0.9, green: 0.7, blue: 0.3, alpha: 1.0)
        static let secondaryGradientEnd = UIColor(red: 0.8, green: 0.5, blue: 0.2, alpha: 1.0)

        static let cardBackground = UIColor.systemBackground
        static let cardShadow = UIColor.black.withAlphaComponent(0.1)

        static let textPrimary = UIColor.label
        static let textSecondary = UIColor.secondaryLabel
        static let textTertiary = UIColor.tertiaryLabel

        static let accent = UIColor.systemBlue
        static let success = UIColor.systemGreen
        static let warning = UIColor.systemOrange
        static let error = UIColor.systemRed
    }

    // MARK: - Fonts
    struct Fonts {
        static let largeTitle = UIFont.systemFont(ofSize: 32, weight: .bold)
        static let title1 = UIFont.systemFont(ofSize: 28, weight: .bold)
        static let title2 = UIFont.systemFont(ofSize: 22, weight: .bold)
        static let title3 = UIFont.systemFont(ofSize: 20, weight: .semibold)
        static let headline = UIFont.systemFont(ofSize: 17, weight: .semibold)
        static let body = UIFont.systemFont(ofSize: 17, weight: .regular)
        static let callout = UIFont.systemFont(ofSize: 16, weight: .regular)
        static let subheadline = UIFont.systemFont(ofSize: 15, weight: .regular)
        static let footnote = UIFont.systemFont(ofSize: 13, weight: .regular)
        static let caption1 = UIFont.systemFont(ofSize: 12, weight: .regular)
        static let caption2 = UIFont.systemFont(ofSize: 11, weight: .regular)
    }

    // MARK: - Spacing
    struct Spacing {
        static let tiny: CGFloat = 4
        static let small: CGFloat = 8
        static let medium: CGFloat = 16
        static let large: CGFloat = 24
        static let extraLarge: CGFloat = 32
        static let huge: CGFloat = 48
    }

    // MARK: - Corner Radius
    struct CornerRadius {
        static let small: CGFloat = 8
        static let medium: CGFloat = 12
        static let large: CGFloat = 16
        static let extraLarge: CGFloat = 24
    }

    // MARK: - Shadow
    struct Shadow {
        static let small = ShadowConfig(
            color: Colors.cardShadow,
            offset: CGSize(width: 0, height: 2),
            radius: 4,
            opacity: 0.1
        )

        static let medium = ShadowConfig(
            color: Colors.cardShadow,
            offset: CGSize(width: 0, height: 4),
            radius: 8,
            opacity: 0.15
        )

        static let large = ShadowConfig(
            color: Colors.cardShadow,
            offset: CGSize(width: 0, height: 8),
            radius: 16,
            opacity: 0.2
        )
    }

    // MARK: - Animation
    struct Animation {
        static let short: TimeInterval = 0.2
        static let medium: TimeInterval = 0.3
        static let long: TimeInterval = 0.5
    }
}

struct ShadowConfig {
    let color: UIColor
    let offset: CGSize
    let radius: CGFloat
    let opacity: Float
}

// MARK: - Leather Constants
extension Constants {
    struct Leather {
        // Material Sources
        static let materialSources = [
            "Cowhide", "Sheepskin", "Pigskin", "Shell Cordovan", "Synthetic"
        ]

        // Tanning Methods
        static let tanningMethods = [
            "Vegetable Tanned", "Chrome Tanned", "Combination Tanned"
        ]

        // Surface Treatments
        static let surfaceTreatments = [
            "Natural", "Embossed", "Smooth", "Nubuck", "Patent"
        ]

        // Texture Descriptions
        static let textureDescriptions = [
            "Firm", "Soft", "Elastic", "Supple"
        ]

        // Thickness Range (mm)
        static let thicknessRange = (min: 0.5, max: 10.0)

        // Default Image Name
        static let defaultImageName = "leather.placeholder"
    }

    // MARK: - Stock & Usage Constants
    struct Stock {
        // Area Units
        static let areaUnits = [
            "sq ft", "sq m", "sq cm"
        ]

        // Currency Units
        static let currencyUnits = [
            "$", "€", "£", "¥", "₹"
        ]

        // Stock Status
        static let stockStatus = [
            "In Stock", "Low Stock", "Out of Stock", "On Order"
        ]

        // Usage Categories
        static let usageCategories = [
            "Wallet", "Bag", "Belt", "Shoe", "Accessory", "Sample", "Waste"
        ]

        // Default low stock threshold
        static let defaultLowStockThreshold: Double = 10.0
    }
}
