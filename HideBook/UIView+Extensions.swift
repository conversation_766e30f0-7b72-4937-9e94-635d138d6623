//
//  UIView+Extensions.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

extension UIView {
    
    // MARK: - Corner Radius
    func setCornerRadius(_ radius: CGFloat) {
        layer.cornerRadius = radius
        layer.masksToBounds = true
    }
    
    func setRoundedCorners(_ corners: UIRectCorner, radius: CGFloat) {
        let path = UIBezierPath(
            roundedRect: bounds,
            byRoundingCorners: corners,
            cornerRadii: CGSize(width: radius, height: radius)
        )
        let mask = CAShapeLayer()
        mask.path = path.cgPath
        layer.mask = mask
    }
    
    // MARK: - Shadow
    func applyShadow(_ config: ShadowConfig) {
        layer.shadowColor = config.color.cgColor
        layer.shadowOffset = config.offset
        layer.shadowRadius = config.radius
        layer.shadowOpacity = config.opacity
        layer.masksToBounds = false
    }
    
    func applyCardShadow() {
        applyShadow(Constants.Shadow.medium)
    }
    
    func removeShadow() {
        layer.shadowOpacity = 0
    }
    
    // MARK: - Border
    func setBorder(width: CGFloat, color: UIColor) {
        layer.borderWidth = width
        layer.borderColor = color.cgColor
    }
    
    func removeBorder() {
        layer.borderWidth = 0
        layer.borderColor = UIColor.clear.cgColor
    }
    
    // MARK: - Gradient
    func applyGradient(colors: [UIColor], direction: GradientDirection = .topToBottom) {
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = colors.map { $0.cgColor }
        gradientLayer.frame = bounds
        
        switch direction {
        case .topToBottom:
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0)
            gradientLayer.endPoint = CGPoint(x: 0.5, y: 1)
        case .leftToRight:
            gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
            gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        case .topLeftToBottomRight:
            gradientLayer.startPoint = CGPoint(x: 0, y: 0)
            gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        case .topRightToBottomLeft:
            gradientLayer.startPoint = CGPoint(x: 1, y: 0)
            gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        }
        
        // Remove existing gradient layers
        layer.sublayers?.removeAll { $0 is CAGradientLayer }
        layer.insertSublayer(gradientLayer, at: 0)
    }
    
    func applyPrimaryGradient() {
        applyGradient(colors: [
            Constants.Colors.primaryGradientStart,
            Constants.Colors.primaryGradientEnd
        ])
    }
    
    func applySecondaryGradient() {
        applyGradient(colors: [
            Constants.Colors.secondaryGradientStart,
            Constants.Colors.secondaryGradientEnd
        ])
    }
    
    // MARK: - Animation
    func fadeIn(duration: TimeInterval = Constants.Animation.medium, completion: (() -> Void)? = nil) {
        alpha = 0
        UIView.animate(withDuration: duration, animations: {
            self.alpha = 1
        }) { _ in
            completion?()
        }
    }
    
    func fadeOut(duration: TimeInterval = Constants.Animation.medium, completion: (() -> Void)? = nil) {
        UIView.animate(withDuration: duration, animations: {
            self.alpha = 0
        }) { _ in
            completion?()
        }
    }
    
    func scaleIn(duration: TimeInterval = Constants.Animation.medium, completion: (() -> Void)? = nil) {
        transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        alpha = 0
        UIView.animate(withDuration: duration, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0, options: .curveEaseInOut, animations: {
            self.transform = CGAffineTransform.identity
            self.alpha = 1
        }) { _ in
            completion?()
        }
    }
    
    func shake(duration: TimeInterval = 0.5) {
        let animation = CAKeyframeAnimation(keyPath: "transform.translation.x")
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.duration = duration
        animation.values = [-20.0, 20.0, -20.0, 20.0, -10.0, 10.0, -5.0, 5.0, 0.0]
        layer.add(animation, forKey: "shake")
    }
    
    // MARK: - Layout Helpers
    func addSubviews(_ views: UIView...) {
        views.forEach { addSubview($0) }
    }
    
    func removeAllSubviews() {
        subviews.forEach { $0.removeFromSuperview() }
    }
    
    // MARK: - Snapshot
    func takeSnapshot() -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(bounds.size, false, UIScreen.main.scale)
        defer { UIGraphicsEndImageContext() }
        
        if let context = UIGraphicsGetCurrentContext() {
            layer.render(in: context)
            return UIGraphicsGetImageFromCurrentImageContext()
        }
        return nil
    }
}

enum GradientDirection {
    case topToBottom
    case leftToRight
    case topLeftToBottomRight
    case topRightToBottomLeft
}
