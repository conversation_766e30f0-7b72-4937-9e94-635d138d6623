//
//  LeatherDetailViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class LeatherDetailViewController: UIViewController {

    // MARK: - Properties
    private let dataManager = LeatherDataManager.shared
    private var leather: LeatherModel

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()

    // Header section
    private let headerView = UIView()
    private let leatherImageView = UIImageView()
    private let nameLabel = UILabel()
    private let favoriteButton = UIButton(type: .system)
    private let basicInfoStackView = UIStackView()

    // Detail sections
    private let basicInfoSection = UIView()
    private let detailInfoSection = UIView()
    private let additionalInfoSection = UIView()

    // MARK: - Initialization
    init(leather: LeatherModel) {
        self.leather = leather
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        populateData()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        gradientBackgroundView.layoutSubviews()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Scroll View
        scrollView.showsVerticalScrollIndicator = false

        // Content View
        contentView.backgroundColor = .clear

        // Setup sections
        setupHeaderView()
        setupBasicInfoSection()
        setupDetailInfoSection()
        setupAdditionalInfoSection()

        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubviews(headerView, basicInfoSection, detailInfoSection, additionalInfoSection)
    }

    private func setupHeaderView() {
        headerView.backgroundColor = .appCardBackground
        headerView.setCornerRadius(Constants.CornerRadius.medium)
        headerView.applyCardShadow()

        // Leather Image
        leatherImageView.contentMode = .scaleAspectFill
        leatherImageView.setCornerRadius(Constants.CornerRadius.medium)
        leatherImageView.backgroundColor = .systemGray5

        // Name Label
        nameLabel.font = Constants.Fonts.title1
        nameLabel.textColor = .appTextPrimary
        nameLabel.numberOfLines = 0

        // Favorite Button
        favoriteButton.setImage(UIImage(systemName: "heart"), for: .normal)
        favoriteButton.setImage(UIImage(systemName: "heart.fill"), for: .selected)
        favoriteButton.tintColor = .appError
        favoriteButton.addTarget(self, action: #selector(favoriteButtonTapped), for: .touchUpInside)

        // Basic Info Stack
        basicInfoStackView.axis = .horizontal
        basicInfoStackView.spacing = Constants.Spacing.large
        basicInfoStackView.distribution = .fillEqually
        basicInfoStackView.alignment = .top

        let materialInfo = createInfoView(title: "材质", value: leather.materialSource, icon: "leaf")
        let tanningInfo = createInfoView(title: "鞣制", value: leather.tanningMethod, icon: "drop")
        let thicknessInfo = createInfoView(title: "厚度", value: leather.thicknessString, icon: "ruler")

        basicInfoStackView.addArrangedSubview(materialInfo)
        basicInfoStackView.addArrangedSubview(tanningInfo)
        basicInfoStackView.addArrangedSubview(thicknessInfo)

        headerView.addSubviews(leatherImageView, nameLabel, favoriteButton, basicInfoStackView)
    }

    private func setupBasicInfoSection() {
        basicInfoSection.backgroundColor = .appCardBackground
        basicInfoSection.setCornerRadius(Constants.CornerRadius.medium)
        basicInfoSection.applyCardShadow()

        let titleLabel = createSectionTitleLabel("基本信息")

        let surfaceInfo = createDetailRow(title: "表面处理", value: leather.surfaceTreatment, icon: "paintbrush")
        let colorInfo = createDetailRow(title: "颜色", value: leather.colorDescription.isEmpty ? "未设定" : leather.colorDescription, icon: "paintpalette")
        let textureInfo = createDetailRow(title: "手感", value: leather.textureDescription.isEmpty ? "未设定" : leather.textureDescription, icon: "hand.raised")

        basicInfoSection.addSubviews(titleLabel, surfaceInfo, colorInfo, textureInfo)

        // Constraints
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        surfaceInfo.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        colorInfo.snp.makeConstraints { make in
            make.top.equalTo(surfaceInfo.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        textureInfo.snp.makeConstraints { make in
            make.top.equalTo(colorInfo.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func setupDetailInfoSection() {
        detailInfoSection.backgroundColor = .appCardBackground
        detailInfoSection.setCornerRadius(Constants.CornerRadius.medium)
        detailInfoSection.applyCardShadow()

        let titleLabel = createSectionTitleLabel("详细信息")

        let originInfo = createDetailRow(title: "产地", value: leather.origin.isEmpty ? "未设定" : leather.origin, icon: "globe")
        let supplierInfo = createDetailRow(title: "供应商", value: leather.supplier.isEmpty ? "未设定" : leather.supplier, icon: "building.2")
        let brandInfo = createDetailRow(title: "品牌", value: leather.brand.isEmpty ? "未设定" : leather.brand, icon: "tag")
        let priceInfo = createDetailRow(title: "价格", value: leather.priceString, icon: "dollarsign.circle")

        detailInfoSection.addSubviews(titleLabel, originInfo, supplierInfo, brandInfo, priceInfo)

        // Constraints
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        originInfo.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        supplierInfo.snp.makeConstraints { make in
            make.top.equalTo(originInfo.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        brandInfo.snp.makeConstraints { make in
            make.top.equalTo(supplierInfo.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        priceInfo.snp.makeConstraints { make in
            make.top.equalTo(brandInfo.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func setupAdditionalInfoSection() {
        additionalInfoSection.backgroundColor = .appCardBackground
        additionalInfoSection.setCornerRadius(Constants.CornerRadius.medium)
        additionalInfoSection.applyCardShadow()

        let titleLabel = createSectionTitleLabel("附加信息")

        // Usage Suggestion
        let usageLabel = createFieldLabel("使用建议")
        let usageTextView = createReadOnlyTextView(text: leather.usageSuggestion.isEmpty ? "暂无使用建议" : leather.usageSuggestion)

        // Notes
        let notesLabel = createFieldLabel("备注")
        let notesTextView = createReadOnlyTextView(text: leather.notes.isEmpty ? "暂无备注" : leather.notes)

        // Timestamps
        let timestampLabel = createFieldLabel("时间信息")
        let createdInfo = createDetailRow(title: "创建时间", value: leather.formattedCreatedDate, icon: "calendar.badge.plus")
        let updatedInfo = createDetailRow(title: "更新时间", value: leather.formattedUpdatedDate, icon: "calendar.badge.clock")

        additionalInfoSection.addSubviews(titleLabel, usageLabel, usageTextView, notesLabel, notesTextView, timestampLabel, createdInfo, updatedInfo)

        // Constraints
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        usageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        usageTextView.snp.makeConstraints { make in
            make.top.equalTo(usageLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(60)
        }

        notesLabel.snp.makeConstraints { make in
            make.top.equalTo(usageTextView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        notesTextView.snp.makeConstraints { make in
            make.top.equalTo(notesLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(60)
        }

        timestampLabel.snp.makeConstraints { make in
            make.top.equalTo(notesTextView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        createdInfo.snp.makeConstraints { make in
            make.top.equalTo(timestampLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        updatedInfo.snp.makeConstraints { make in
            make.top.equalTo(createdInfo.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        leatherImageView.snp.makeConstraints { make in
            make.top.leading.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.height.equalTo(120)
        }

        nameLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.leading.equalTo(leatherImageView.snp.trailing).offset(Constants.Spacing.medium)
            make.trailing.equalTo(favoriteButton.snp.leading).offset(-Constants.Spacing.small)
        }

        favoriteButton.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.height.equalTo(32)
        }

        basicInfoStackView.snp.makeConstraints { make in
            make.top.equalTo(leatherImageView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }

        basicInfoSection.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        detailInfoSection.snp.makeConstraints { make in
            make.top.equalTo(basicInfoSection.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        additionalInfoSection.snp.makeConstraints { make in
            make.top.equalTo(detailInfoSection.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.large)
        }
    }

    private func setupNavigationBar() {
        title = "皮革详情"

        let editButton = UIBarButtonItem(
            barButtonSystemItem: .edit,
            target: self,
            action: #selector(editButtonTapped)
        )

        let shareButton = UIBarButtonItem(
            barButtonSystemItem: .action,
            target: self,
            action: #selector(shareButtonTapped)
        )

        navigationItem.rightBarButtonItems = [editButton, shareButton]
    }

    // MARK: - Helper Methods
    private func createSectionTitleLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = Constants.Fonts.title3
        label.textColor = .appTextPrimary
        return label
    }

    private func createFieldLabel(_ text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = Constants.Fonts.callout
        label.textColor = .appTextSecondary
        return label
    }

    private func createInfoView(title: String, value: String, icon: String) -> UIView {
        let containerView = UIView()

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = .appAccent
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.caption1
        titleLabel.textColor = .appTextSecondary
        titleLabel.textAlignment = .center

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = Constants.Fonts.callout
        valueLabel.textColor = .appTextPrimary
        valueLabel.textAlignment = .center
        valueLabel.numberOfLines = 2

        containerView.addSubviews(iconImageView, titleLabel, valueLabel)

        iconImageView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.bottom.equalToSuperview()
        }

        return containerView
    }

    private func createDetailRow(title: String, value: String, icon: String) -> UIView {
        let containerView = UIView()

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = .appAccent
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.callout
        titleLabel.textColor = .appTextSecondary

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = Constants.Fonts.body
        valueLabel.textColor = .appTextPrimary
        valueLabel.numberOfLines = 0

        containerView.addSubviews(iconImageView, titleLabel, valueLabel)

        iconImageView.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.leading.equalTo(iconImageView.snp.trailing).offset(Constants.Spacing.small)
            make.top.equalToSuperview()
            make.width.equalTo(80)
        }

        valueLabel.snp.makeConstraints { make in
            make.leading.equalTo(titleLabel.snp.trailing).offset(Constants.Spacing.small)
            make.trailing.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }

        containerView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(32)
        }

        return containerView
    }

    private func createReadOnlyTextView(text: String) -> UITextView {
        let textView = UITextView()
        textView.text = text
        textView.font = Constants.Fonts.body
        textView.textColor = .appTextPrimary
        textView.backgroundColor = .systemGray6
        textView.setCornerRadius(Constants.CornerRadius.small)
        textView.isEditable = false
        textView.isScrollEnabled = false
        textView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        return textView
    }

    // MARK: - Data Population
    private func populateData() {
        nameLabel.text = leather.displayName
        favoriteButton.isSelected = leather.isFavorite

        // Configure image
        if let imageData = leather.imageData, let image = UIImage(data: imageData) {
            leatherImageView.image = image
            leatherImageView.contentMode = .scaleAspectFill
        } else {
            leatherImageView.image = UIImage(systemName: "photo")
            leatherImageView.contentMode = .center
            leatherImageView.tintColor = .systemGray3
        }
    }

    // MARK: - Actions
    @objc private func favoriteButtonTapped() {
        var updatedLeather = leather
        updatedLeather.toggleFavorite()
        dataManager.updateLeather(updatedLeather)

        self.leather = updatedLeather
        favoriteButton.isSelected = updatedLeather.isFavorite

        // Add animation
        UIView.animate(withDuration: 0.2, delay: 0, usingSpringWithDamping: 0.6, initialSpringVelocity: 0.8, options: .curveEaseInOut) {
            self.favoriteButton.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        } completion: { _ in
            UIView.animate(withDuration: 0.1) {
                self.favoriteButton.transform = CGAffineTransform.identity
            }
        }
    }

    @objc private func editButtonTapped() {
        let editViewController = AddLeatherViewController(leather: leather)
        let navigationController = UINavigationController(rootViewController: editViewController)
        present(navigationController, animated: true)
    }

    @objc private func shareButtonTapped() {
        let shareText = generateShareText()
        let activityViewController = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        if let popover = activityViewController.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.last
        }

        present(activityViewController, animated: true)
    }

    private func generateShareText() -> String {
        var shareText = "皮革资料：\(leather.displayName)\n\n"
        shareText += "材质来源：\(leather.materialSource)\n"
        shareText += "鞣制方式：\(leather.tanningMethod)\n"
        shareText += "表面处理：\(leather.surfaceTreatment)\n"
        shareText += "厚度：\(leather.thicknessString)\n"

        if !leather.colorDescription.isEmpty {
            shareText += "颜色：\(leather.colorDescription)\n"
        }

        if !leather.origin.isEmpty {
            shareText += "产地：\(leather.origin)\n"
        }

        if !leather.supplier.isEmpty {
            shareText += "供应商：\(leather.supplier)\n"
        }

        if !leather.brand.isEmpty {
            shareText += "品牌：\(leather.brand)\n"
        }

        if leather.price != nil {
            shareText += "价格：\(leather.priceString)\n"
        }

        if !leather.usageSuggestion.isEmpty {
            shareText += "\n使用建议：\(leather.usageSuggestion)\n"
        }

        shareText += "\n来自 HideBook 皮革资料库"

        return shareText
    }
}
