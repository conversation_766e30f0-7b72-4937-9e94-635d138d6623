//
//  UIColor+Extensions.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

extension UIColor {
    
    // MARK: - Convenience Initializers
    convenience init(hex: String, alpha: CGFloat = 1.0) {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")
        
        var rgb: UInt64 = 0
        
        Scanner(string: hexSanitized).scanHexInt64(&rgb)
        
        let red = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
        let green = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
        let blue = CGFloat(rgb & 0x0000FF) / 255.0
        
        self.init(red: red, green: green, blue: blue, alpha: alpha)
    }
    
    convenience init(red: Int, green: Int, blue: Int, alpha: CGFloat = 1.0) {
        self.init(
            red: CGFloat(red) / 255.0,
            green: CGFloat(green) / 255.0,
            blue: CGFloat(blue) / 255.0,
            alpha: alpha
        )
    }
    
    // MARK: - App Theme Colors
    static var appPrimaryGradientStart: UIColor {
        return Constants.Colors.primaryGradientStart
    }
    
    static var appPrimaryGradientEnd: UIColor {
        return Constants.Colors.primaryGradientEnd
    }
    
    static var appSecondaryGradientStart: UIColor {
        return Constants.Colors.secondaryGradientStart
    }
    
    static var appSecondaryGradientEnd: UIColor {
        return Constants.Colors.secondaryGradientEnd
    }
    
    static var appCardBackground: UIColor {
        return Constants.Colors.cardBackground
    }
    
    static var appCardShadow: UIColor {
        return Constants.Colors.cardShadow
    }
    
    static var appTextPrimary: UIColor {
        return Constants.Colors.textPrimary
    }
    
    static var appTextSecondary: UIColor {
        return Constants.Colors.textSecondary
    }
    
    static var appTextTertiary: UIColor {
        return Constants.Colors.textTertiary
    }
    
    static var appAccent: UIColor {
        return Constants.Colors.accent
    }
    
    static var appSuccess: UIColor {
        return Constants.Colors.success
    }
    
    static var appWarning: UIColor {
        return Constants.Colors.warning
    }
    
    static var appError: UIColor {
        return Constants.Colors.error
    }
    
    // MARK: - Utility Methods
    func toHexString() -> String {
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        
        getRed(&r, green: &g, blue: &b, alpha: &a)
        
        let rgb: Int = (Int)(r*255)<<16 | (Int)(g*255)<<8 | (Int)(b*255)<<0
        
        return String(format:"#%06x", rgb)
    }
    
    func lighter(by percentage: CGFloat = 30.0) -> UIColor? {
        return self.adjust(by: abs(percentage))
    }
    
    func darker(by percentage: CGFloat = 30.0) -> UIColor? {
        return self.adjust(by: -1 * abs(percentage))
    }
    
    private func adjust(by percentage: CGFloat = 30.0) -> UIColor? {
        var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
        if self.getRed(&red, green: &green, blue: &blue, alpha: &alpha) {
            return UIColor(red: min(red + percentage/100, 1.0),
                          green: min(green + percentage/100, 1.0),
                          blue: min(blue + percentage/100, 1.0),
                          alpha: alpha)
        } else {
            return nil
        }
    }
}
