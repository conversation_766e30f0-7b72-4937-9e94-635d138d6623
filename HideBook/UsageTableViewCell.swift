//
//  UsageTableViewCell.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class UsageTableViewCell: UITableViewCell {
    
    // MARK: - Properties
    static let identifier = "UsageTableViewCell"
    
    // MARK: - UI Components
    private let containerView = UIView()
    private let gradientView = GradientView()
    private let projectNameLabel = UILabel()
    private let leatherNameLabel = UILabel()
    private let categoryLabel = UILabel()
    private let quantityUsedLabel = UILabel()
    private let usageDateLabel = UILabel()
    private let categoryIconView = UIImageView()
    private let detailStackView = UIStackView()
    private let topStackView = UIStackView()
    private let bottomStackView = UIStackView()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func prepareForReuse() {
        super.prepareForReuse()
        projectNameLabel.text = nil
        leatherNameLabel.text = nil
        categoryLabel.text = nil
        quantityUsedLabel.text = nil
        usageDateLabel.text = nil
        categoryIconView.image = nil
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        gradientView.layoutSubviews()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // Container View
        containerView.backgroundColor = .appCardBackground
        containerView.setCornerRadius(Constants.CornerRadius.medium)
        containerView.applyCardShadow()
        
        // Gradient View
        gradientView.setSecondaryGradient()
        gradientView.setCornerRadius(Constants.CornerRadius.medium)
        gradientView.alpha = 0.1
        
        // Project Name Label
        projectNameLabel.font = Constants.Fonts.headline
        projectNameLabel.textColor = .appTextPrimary
        projectNameLabel.numberOfLines = 1
        
        // Leather Name Label
        leatherNameLabel.font = Constants.Fonts.subheadline
        leatherNameLabel.textColor = .appTextSecondary
        leatherNameLabel.numberOfLines = 1
        
        // Category Label
        categoryLabel.font = Constants.Fonts.caption1
        categoryLabel.textColor = .white
        categoryLabel.textAlignment = .center
        categoryLabel.backgroundColor = .appAccent
        categoryLabel.setCornerRadius(Constants.CornerRadius.small / 2)
        
        // Quantity Used Label
        quantityUsedLabel.font = Constants.Fonts.title3
        quantityUsedLabel.textColor = .appError
        quantityUsedLabel.textAlignment = .right
        
        // Usage Date Label
        usageDateLabel.font = Constants.Fonts.caption1
        usageDateLabel.textColor = .appTextTertiary
        usageDateLabel.textAlignment = .right
        
        // Category Icon View
        categoryIconView.contentMode = .scaleAspectFit
        categoryIconView.tintColor = .appAccent
        
        // Stack Views
        topStackView.axis = .horizontal
        topStackView.spacing = Constants.Spacing.small
        topStackView.alignment = .center
        topStackView.distribution = .fill
        
        bottomStackView.axis = .horizontal
        bottomStackView.spacing = Constants.Spacing.small
        bottomStackView.alignment = .center
        bottomStackView.distribution = .fill
        
        detailStackView.axis = .vertical
        detailStackView.spacing = Constants.Spacing.tiny
        detailStackView.alignment = .leading
        detailStackView.distribution = .fill
        
        // Add subviews
        contentView.addSubview(containerView)
        containerView.addSubviews(gradientView, detailStackView, quantityUsedLabel, usageDateLabel)
        
        topStackView.addArrangedSubview(categoryIconView)
        topStackView.addArrangedSubview(projectNameLabel)
        topStackView.addArrangedSubview(categoryLabel)
        
        bottomStackView.addArrangedSubview(leatherNameLabel)
        bottomStackView.addArrangedSubview(UIView()) // Spacer
        
        detailStackView.addArrangedSubview(topStackView)
        detailStackView.addArrangedSubview(bottomStackView)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(Constants.Spacing.small)
        }
        
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        categoryIconView.snp.makeConstraints { make in
            make.width.height.equalTo(20)
        }
        
        categoryLabel.snp.makeConstraints { make in
            make.width.greaterThanOrEqualTo(50)
            make.height.equalTo(20)
        }
        
        detailStackView.snp.makeConstraints { make in
            make.leading.equalToSuperview().inset(Constants.Spacing.medium)
            make.top.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalTo(quantityUsedLabel.snp.leading).offset(-Constants.Spacing.small)
        }
        
        quantityUsedLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.greaterThanOrEqualTo(80)
        }
        
        usageDateLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.greaterThanOrEqualTo(80)
        }
        
        topStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }
        
        bottomStackView.snp.makeConstraints { make in
            make.leading.trailing.equalToSuperview()
        }
    }
    
    // MARK: - Configuration
    func configure(with usageRecord: UsageRecord) {
        projectNameLabel.text = usageRecord.projectName
        leatherNameLabel.text = usageRecord.leatherName
        categoryLabel.text = usageRecord.category
        quantityUsedLabel.text = "- \(usageRecord.formattedQuantityUsed)"
        usageDateLabel.text = usageRecord.formattedUsageDate
        
        // Configure category icon
        categoryIconView.image = getCategoryIcon(for: usageRecord.category)
        
        // Animate appearance
        containerView.scaleIn()
    }
    
    private func getCategoryIcon(for category: String) -> UIImage? {
        switch category.lowercased() {
        case "wallet":
            return UIImage(systemName: "creditcard")
        case "bag":
            return UIImage(systemName: "bag")
        case "belt":
            return UIImage(systemName: "circle.dotted")
        case "shoe":
            return UIImage(systemName: "shoe")
        case "accessory":
            return UIImage(systemName: "star")
        case "sample":
            return UIImage(systemName: "testtube.2")
        case "waste":
            return UIImage(systemName: "trash")
        default:
            return UIImage(systemName: "questionmark.circle")
        }
    }
}
