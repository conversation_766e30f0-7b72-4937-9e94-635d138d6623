//
//  DashboardViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit
import AAInfographics

class DashboardViewController: UIViewController {

    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    private let leatherDataManager = LeatherDataManager.shared

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()

    // Dashboard Components
    private let welcomeLabel = UILabel()
    private let summaryCardsStackView = UIStackView()
    private let quickActionsView = UIView()
    private let recentActivityView = UIView()
    private let alertsView = UIView()
    private let miniChartView = AAChartView()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        populateData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshData()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Scroll View
        scrollView.showsVerticalScrollIndicator = false
        scrollView.refreshControl = UIRefreshControl()
        scrollView.refreshControl?.addTarget(self, action: #selector(refreshData), for: .valueChanged)

        // Content View
        contentView.backgroundColor = .clear

        // Welcome Label
        welcomeLabel.text = "Welcome to HideBook"
        welcomeLabel.font = Constants.Fonts.largeTitle
        welcomeLabel.textColor = .appTextPrimary

        // Summary Cards Stack View
        summaryCardsStackView.axis = .horizontal
        summaryCardsStackView.spacing = Constants.Spacing.medium
        summaryCardsStackView.distribution = .fillEqually

        setupQuickActionsView()
        setupRecentActivityView()
        setupAlertsView()

        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
        contentView.addSubviews(
            welcomeLabel,
            summaryCardsStackView,
            quickActionsView,
            createChartSection(),
            recentActivityView,
            alertsView
        )
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        welcomeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        summaryCardsStackView.snp.makeConstraints { make in
            make.top.equalTo(welcomeLabel.snp.bottom).offset(Constants.Spacing.large)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(100)
        }

        quickActionsView.snp.makeConstraints { make in
            make.top.equalTo(summaryCardsStackView.snp.bottom).offset(Constants.Spacing.large)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        let chartSection = contentView.subviews.first { $0.subviews.contains(miniChartView) }
        chartSection?.snp.makeConstraints { make in
            make.top.equalTo(quickActionsView.snp.bottom).offset(Constants.Spacing.large)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        recentActivityView.snp.makeConstraints { make in
            make.top.equalTo(chartSection?.snp.bottom ?? quickActionsView.snp.bottom).offset(Constants.Spacing.large)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        alertsView.snp.makeConstraints { make in
            make.top.equalTo(recentActivityView.snp.bottom).offset(Constants.Spacing.large)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.large)
        }
    }

    private func setupNavigationBar() {
        title = "Dashboard"
        navigationController?.navigationBar.prefersLargeTitles = true

        let settingsButton = UIBarButtonItem(
            image: UIImage(systemName: "gearshape"),
            style: .plain,
            target: self,
            action: #selector(settingsButtonTapped)
        )

        navigationItem.rightBarButtonItem = settingsButton
    }

    private func setupQuickActionsView() {
        quickActionsView.backgroundColor = .appCardBackground
        quickActionsView.setCornerRadius(Constants.CornerRadius.medium)
        quickActionsView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = "Quick Actions"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        let actionsStackView = UIStackView()
        actionsStackView.axis = .horizontal
        actionsStackView.spacing = Constants.Spacing.medium
        actionsStackView.distribution = .fillEqually

        let addLeatherButton = createActionButton(
            title: "Add Leather",
            icon: "plus.circle",
            color: .appAccent,
            action: #selector(addLeatherTapped)
        )

        let addStockButton = createActionButton(
            title: "Add Stock",
            icon: "cube.box",
            color: .appSuccess,
            action: #selector(addStockTapped)
        )

        let recordUsageButton = createActionButton(
            title: "Record Usage",
            icon: "minus.circle",
            color: .appWarning,
            action: #selector(recordUsageTapped)
        )

        actionsStackView.addArrangedSubview(addLeatherButton)
        actionsStackView.addArrangedSubview(addStockButton)
        actionsStackView.addArrangedSubview(recordUsageButton)

        quickActionsView.addSubviews(titleLabel, actionsStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        actionsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(80)
        }
    }

    private func setupRecentActivityView() {
        recentActivityView.backgroundColor = .appCardBackground
        recentActivityView.setCornerRadius(Constants.CornerRadius.medium)
        recentActivityView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = "Recent Activity"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        let activityStackView = UIStackView()
        activityStackView.axis = .vertical
        activityStackView.spacing = Constants.Spacing.small

        recentActivityView.addSubviews(titleLabel, activityStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        activityStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func setupAlertsView() {
        alertsView.backgroundColor = .appCardBackground
        alertsView.setCornerRadius(Constants.CornerRadius.medium)
        alertsView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = "Alerts"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        let alertsStackView = UIStackView()
        alertsStackView.axis = .vertical
        alertsStackView.spacing = Constants.Spacing.small

        alertsView.addSubviews(titleLabel, alertsStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        alertsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }

    private func createChartSection() -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = "Stock Overview"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        miniChartView.backgroundColor = .clear
        miniChartView.setCornerRadius(Constants.CornerRadius.small)

        sectionView.addSubviews(titleLabel, miniChartView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        miniChartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(200)
        }

        return sectionView
    }

    private func createActionButton(title: String, icon: String, color: UIColor, action: Selector) -> UIButton {
        let button = UIButton(type: .system)
        button.backgroundColor = color.withAlphaComponent(0.1)
        button.setCornerRadius(Constants.CornerRadius.medium)
        button.addTarget(self, action: action, for: .touchUpInside)

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.caption1
        titleLabel.textColor = color
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 2

        button.addSubviews(iconImageView, titleLabel)

        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.small)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.tiny)
            make.bottom.equalToSuperview().inset(Constants.Spacing.small)
        }

        return button
    }

    // MARK: - Data Population
    private func populateData() {
        setupSummaryCards()
        setupMiniChart()
        populateRecentActivity()
        populateAlerts()
    }

    @objc private func refreshData() {
        populateData()
        scrollView.refreshControl?.endRefreshing()
    }

    private func setupSummaryCards() {
        // Clear existing cards
        summaryCardsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        let statistics = stockDataManager.getStockStatistics()

        let totalLeathersCard = createSummaryCard(
            title: "Total Leathers",
            value: "\(leatherDataManager.allLeathers.count)",
            icon: "doc.text",
            color: .appAccent
        )

        let totalStockCard = createSummaryCard(
            title: "Stock Entries",
            value: "\(statistics.totalEntries)",
            icon: "cube.box",
            color: .appSuccess
        )

        let totalValueCard = createSummaryCard(
            title: "Total Value",
            value: String(format: "$%.0f", statistics.totalValue),
            icon: "dollarsign.circle",
            color: .appWarning
        )

        summaryCardsStackView.addArrangedSubview(totalLeathersCard)
        summaryCardsStackView.addArrangedSubview(totalStockCard)
        summaryCardsStackView.addArrangedSubview(totalValueCard)
    }

    private func createSummaryCard(title: String, value: String, icon: String, color: UIColor) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = .appCardBackground
        cardView.setCornerRadius(Constants.CornerRadius.medium)
        cardView.applyCardShadow()

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = Constants.Fonts.title2
        valueLabel.textColor = .appTextPrimary
        valueLabel.textAlignment = .center

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.caption1
        titleLabel.textColor = .appTextSecondary
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 2

        cardView.addSubviews(iconImageView, valueLabel, titleLabel)

        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.small)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(20)
        }

        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.tiny)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.tiny)
            make.bottom.equalToSuperview().inset(Constants.Spacing.small)
        }

        return cardView
    }

    private func setupMiniChart() {
        let stockEntries = stockDataManager.allStockEntries
        let inStockCount = stockEntries.filter { $0.stockStatus == "In Stock" }.count
        let lowStockCount = stockEntries.filter { $0.stockStatus == "Low Stock" }.count
        let outOfStockCount = stockEntries.filter { $0.stockStatus == "Out of Stock" }.count

        let chartModel = AAChartModel()
            .chartType(.pie)
            .title("")
            .subtitle("")
            .dataLabelsEnabled(true)
            .legendEnabled(true)
            .series([
                AASeriesElement()
                    .name("Stock Status")
                    .data([
                        ["In Stock", inStockCount],
                        ["Low Stock", lowStockCount],
                        ["Out of Stock", outOfStockCount]
                    ])
                    .colorByPoint(true)
            ])

        miniChartView.aa_drawChartWithChartModel(chartModel)
    }

    private func populateRecentActivity() {
        guard let activityStackView = recentActivityView.subviews.compactMap({ $0 as? UIStackView }).first else { return }

        // Clear existing activities
        activityStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        let recentUsage = stockDataManager.allUsageRecords
            .sorted { $0.usageDate > $1.usageDate }
            .prefix(3)

        if recentUsage.isEmpty {
            let noActivityLabel = UILabel()
            noActivityLabel.text = "No recent activity"
            noActivityLabel.font = Constants.Fonts.body
            noActivityLabel.textColor = .appTextTertiary
            noActivityLabel.textAlignment = .center
            activityStackView.addArrangedSubview(noActivityLabel)
        } else {
            for usage in recentUsage {
                let activityView = createActivityItem(
                    title: usage.projectName,
                    subtitle: "Used \(usage.formattedQuantityUsed)",
                    date: usage.formattedUsageDate
                )
                activityStackView.addArrangedSubview(activityView)
            }
        }
    }

    private func populateAlerts() {
        guard let alertsStackView = alertsView.subviews.compactMap({ $0 as? UIStackView }).first else { return }

        // Clear existing alerts
        alertsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        let lowStockEntries = stockDataManager.lowStockEntries
        let outOfStockEntries = stockDataManager.outOfStockEntries

        if lowStockEntries.isEmpty && outOfStockEntries.isEmpty {
            let noAlertsLabel = UILabel()
            noAlertsLabel.text = "No alerts"
            noAlertsLabel.font = Constants.Fonts.body
            noAlertsLabel.textColor = .appTextTertiary
            noAlertsLabel.textAlignment = .center
            alertsStackView.addArrangedSubview(noAlertsLabel)
        } else {
            for entry in outOfStockEntries.prefix(2) {
                let alertView = createAlertItem(
                    title: "Out of Stock",
                    message: entry.leatherName,
                    type: .error
                )
                alertsStackView.addArrangedSubview(alertView)
            }

            for entry in lowStockEntries.prefix(3) {
                let alertView = createAlertItem(
                    title: "Low Stock",
                    message: "\(entry.leatherName) - \(entry.formattedQuantity)",
                    type: .warning
                )
                alertsStackView.addArrangedSubview(alertView)
            }
        }
    }

    private func createActivityItem(title: String, subtitle: String, date: String) -> UIView {
        let itemView = UIView()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.callout
        titleLabel.textColor = .appTextPrimary

        let subtitleLabel = UILabel()
        subtitleLabel.text = subtitle
        subtitleLabel.font = Constants.Fonts.caption1
        subtitleLabel.textColor = .appTextSecondary

        let dateLabel = UILabel()
        dateLabel.text = date
        dateLabel.font = Constants.Fonts.caption2
        dateLabel.textColor = .appTextTertiary
        dateLabel.textAlignment = .right

        itemView.addSubviews(titleLabel, subtitleLabel, dateLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.equalToSuperview()
            make.trailing.equalTo(dateLabel.snp.leading).offset(-Constants.Spacing.small)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(2)
            make.leading.equalToSuperview()
            make.trailing.equalTo(dateLabel.snp.leading).offset(-Constants.Spacing.small)
            make.bottom.equalToSuperview()
        }

        dateLabel.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview()
            make.width.equalTo(80)
        }

        itemView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(40)
        }

        return itemView
    }

    private func createAlertItem(title: String, message: String, type: AlertType) -> UIView {
        let itemView = UIView()
        itemView.backgroundColor = type.backgroundColor
        itemView.setCornerRadius(Constants.CornerRadius.small)

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: type.iconName)
        iconImageView.tintColor = type.color
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.callout
        titleLabel.textColor = type.color

        let messageLabel = UILabel()
        messageLabel.text = message
        messageLabel.font = Constants.Fonts.caption1
        messageLabel.textColor = .appTextSecondary
        messageLabel.numberOfLines = 2

        itemView.addSubviews(iconImageView, titleLabel, messageLabel)

        iconImageView.snp.makeConstraints { make in
            make.leading.centerY.equalToSuperview().inset(Constants.Spacing.small)
            make.width.height.equalTo(16)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.small)
            make.leading.equalTo(iconImageView.snp.trailing).offset(Constants.Spacing.small)
            make.trailing.equalToSuperview().inset(Constants.Spacing.small)
        }

        messageLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(2)
            make.leading.equalTo(iconImageView.snp.trailing).offset(Constants.Spacing.small)
            make.trailing.bottom.equalToSuperview().inset(Constants.Spacing.small)
        }

        itemView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(50)
        }

        return itemView
    }

    // MARK: - Actions
    @objc private func settingsButtonTapped() {
        // TODO: Implement settings
        let alertController = UIAlertController(
            title: "Settings",
            message: "Settings functionality coming soon!",
            preferredStyle: .alert
        )
        alertController.addAction(UIAlertAction(title: "OK", style: .default))
        present(alertController, animated: true)
    }

    @objc private func addLeatherTapped() {
        let addLeatherVC = AddLeatherViewController()
        let navController = UINavigationController(rootViewController: addLeatherVC)
        present(navController, animated: true)
    }

    @objc private func addStockTapped() {
        let addStockVC = AddStockEntryViewController()
        let navController = UINavigationController(rootViewController: addStockVC)
        present(navController, animated: true)
    }

    @objc private func recordUsageTapped() {
        let addUsageVC = AddUsageRecordViewController()
        let navController = UINavigationController(rootViewController: addUsageVC)
        present(navController, animated: true)
    }
}

// MARK: - Alert Type
enum AlertType {
    case warning
    case error

    var color: UIColor {
        switch self {
        case .warning: return .appWarning
        case .error: return .appError
        }
    }

    var backgroundColor: UIColor {
        return color.withAlphaComponent(0.1)
    }

    var iconName: String {
        switch self {
        case .warning: return "exclamationmark.triangle"
        case .error: return "xmark.circle"
        }
    }
}
