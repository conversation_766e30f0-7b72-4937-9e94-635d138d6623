//
//  GradientView.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

class GradientView: UIView {
    
    // MARK: - Properties
    private var gradientLayer: CAGradientLayer?
    
    var colors: [UIColor] = [] {
        didSet {
            updateGradient()
        }
    }
    
    var direction: GradientDirection = .topToBottom {
        didSet {
            updateGradient()
        }
    }
    
    var locations: [NSNumber]? {
        didSet {
            updateGradient()
        }
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupGradient()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupGradient()
    }
    
    convenience init(colors: [UIColor], direction: GradientDirection = .topToBottom) {
        self.init(frame: .zero)
        self.colors = colors
        self.direction = direction
        updateGradient()
    }
    
    // MARK: - Lifecycle
    override func layoutSubviews() {
        super.layoutSubviews()
        gradientLayer?.frame = bounds
    }
    
    // MARK: - Setup
    private func setupGradient() {
        gradientLayer = CAGradientLayer()
        guard let gradientLayer = gradientLayer else { return }
        
        layer.insertSublayer(gradientLayer, at: 0)
    }
    
    private func updateGradient() {
        guard let gradientLayer = gradientLayer else { return }
        
        gradientLayer.colors = colors.map { $0.cgColor }
        gradientLayer.locations = locations
        
        switch direction {
        case .topToBottom:
            gradientLayer.startPoint = CGPoint(x: 0.5, y: 0)
            gradientLayer.endPoint = CGPoint(x: 0.5, y: 1)
        case .leftToRight:
            gradientLayer.startPoint = CGPoint(x: 0, y: 0.5)
            gradientLayer.endPoint = CGPoint(x: 1, y: 0.5)
        case .topLeftToBottomRight:
            gradientLayer.startPoint = CGPoint(x: 0, y: 0)
            gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        case .topRightToBottomLeft:
            gradientLayer.startPoint = CGPoint(x: 1, y: 0)
            gradientLayer.endPoint = CGPoint(x: 0, y: 1)
        }
        
        gradientLayer.frame = bounds
    }
    
    // MARK: - Public Methods
    func setPrimaryGradient() {
        colors = [
            Constants.Colors.primaryGradientStart,
            Constants.Colors.primaryGradientEnd
        ]
    }
    
    func setSecondaryGradient() {
        colors = [
            Constants.Colors.secondaryGradientStart,
            Constants.Colors.secondaryGradientEnd
        ]
    }
    
    func setCustomGradient(startColor: UIColor, endColor: UIColor) {
        colors = [startColor, endColor]
    }
    
    func animateGradientChange(to newColors: [UIColor], duration: TimeInterval = Constants.Animation.medium) {
        let animation = CABasicAnimation(keyPath: "colors")
        animation.fromValue = gradientLayer?.colors
        animation.toValue = newColors.map { $0.cgColor }
        animation.duration = duration
        animation.fillMode = .forwards
        animation.isRemovedOnCompletion = false
        
        gradientLayer?.add(animation, forKey: "colorChange")
        colors = newColors
    }
}

// MARK: - Factory Methods
extension GradientView {
    
    static func primaryGradient() -> GradientView {
        let view = GradientView()
        view.setPrimaryGradient()
        return view
    }
    
    static func secondaryGradient() -> GradientView {
        let view = GradientView()
        view.setSecondaryGradient()
        return view
    }
    
    static func customGradient(startColor: UIColor, endColor: UIColor, direction: GradientDirection = .topToBottom) -> GradientView {
        return GradientView(colors: [startColor, endColor], direction: direction)
    }
}
