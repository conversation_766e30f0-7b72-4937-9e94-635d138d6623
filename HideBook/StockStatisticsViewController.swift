//
//  StockStatisticsViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit
import AAInfographics

class StockStatisticsViewController: UIViewController {

    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared

    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()

    // Chart Views
    private let stockStatusChartView = AAChartView()
    private let stockValueChartView = AAChartView()
    private let usageTrendChartView = AAChartView()
    private let supplierDistributionChartView = AAChartView()
    private let monthlyUsageChartView = AAChartView()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        populateData()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Scroll View
        scrollView.showsVerticalScrollIndicator = false

        // Content View
        contentView.backgroundColor = .clear

        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    }

    private func setupNavigationBar() {
        title = "Stock Statistics"

        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
    }

    private func populateData() {
        let statistics = stockDataManager.getStockStatistics()

        // Create sections with charts
        let sections = [
            createOverviewSection(statistics: statistics),
            createChartSection(title: "Stock Status Distribution", chartView: stockStatusChartView),
            createChartSection(title: "Stock Value by Supplier", chartView: stockValueChartView),
            createChartSection(title: "Usage Trend (Last 6 Months)", chartView: usageTrendChartView),
            createChartSection(title: "Supplier Distribution", chartView: supplierDistributionChartView),
            createChartSection(title: "Monthly Usage Pattern", chartView: monthlyUsageChartView)
        ]

        sections.forEach { contentView.addSubview($0) }

        // Setup constraints for sections
        for (index, section) in sections.enumerated() {
            section.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)

                if index == 0 {
                    make.top.equalToSuperview().inset(Constants.Spacing.medium)
                } else {
                    make.top.equalTo(sections[index - 1].snp.bottom).offset(Constants.Spacing.medium)
                }

                if index == sections.count - 1 {
                    make.bottom.equalToSuperview().inset(Constants.Spacing.large)
                }
            }
        }

        // Configure charts
        setupCharts(statistics: statistics)
    }

    private func createOverviewSection(statistics: StockStatistics) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = "Overview"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        let statsStackView = UIStackView()
        statsStackView.axis = .horizontal
        statsStackView.spacing = Constants.Spacing.medium
        statsStackView.distribution = .fillEqually

        let totalEntriesCard = createStatCard(
            title: "Total Entries",
            value: "\(statistics.totalEntries)",
            icon: "cube.box",
            color: .appAccent
        )

        let totalValueCard = createStatCard(
            title: "Total Value",
            value: String(format: "$%.2f", statistics.totalValue),
            icon: "dollarsign.circle",
            color: .appSuccess
        )

        let totalQuantityCard = createStatCard(
            title: "Total Quantity",
            value: String(format: "%.1f", statistics.totalQuantity),
            icon: "scalemass",
            color: .appWarning
        )

        statsStackView.addArrangedSubview(totalEntriesCard)
        statsStackView.addArrangedSubview(totalValueCard)
        statsStackView.addArrangedSubview(totalQuantityCard)

        sectionView.addSubviews(titleLabel, statsStackView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }

        return sectionView
    }

    private func createChartSection(title: String, chartView: AAChartView) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary

        chartView.backgroundColor = .clear
        chartView.setCornerRadius(Constants.CornerRadius.small)

        sectionView.addSubviews(titleLabel, chartView)

        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }

        chartView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
            make.height.equalTo(250)
        }

        return sectionView
    }

    private func setupCharts(statistics: StockStatistics) {
        setupStockStatusChart()
        setupStockValueChart(statistics: statistics)
        setupUsageTrendChart()
        setupSupplierDistributionChart(statistics: statistics)
        setupMonthlyUsageChart()
    }

    private func setupStockStatusChart() {
        let stockEntries = stockDataManager.allStockEntries
        let inStockCount = stockEntries.filter { $0.stockStatus == "In Stock" }.count
        let lowStockCount = stockEntries.filter { $0.stockStatus == "Low Stock" }.count
        let outOfStockCount = stockEntries.filter { $0.stockStatus == "Out of Stock" }.count

        let chartModel = AAChartModel()
            .chartType(.pie)
            .title("Stock Status Distribution")
            .subtitle("Current inventory status")
            .dataLabelsEnabled(true)
            .yAxisTitle("Count")
            .series([
                AASeriesElement()
                    .name("Stock Status")
                    .data([
                        ["In Stock", inStockCount],
                        ["Low Stock", lowStockCount],
                        ["Out of Stock", outOfStockCount]
                    ])
                    .colorByPoint(true)
            ])
            .colors(["#28a745", "#ffc107", "#dc3545"])

        stockStatusChartView.aa_drawChartWithChartModel(chartModel)
    }

    private func setupStockValueChart(statistics: StockStatistics) {
        let suppliers = Array(statistics.supplierCounts.keys)
        let values = suppliers.map { supplier in
            stockDataManager.allStockEntries
                .filter { $0.supplier == supplier }
                .reduce(0.0) { $0 + $1.totalCost }
        }

        let chartModel = AAChartModel()
            .chartType(.column)
            .title("Stock Value by Supplier")
            .subtitle("Total investment per supplier")
            .dataLabelsEnabled(true)
            .yAxisTitle("Value ($)")
            .categories(suppliers)
            .series([
                AASeriesElement()
                    .name("Stock Value")
                    .data(values)
                    .color("#007bff")
            ])

        stockValueChartView.aa_drawChartWithChartModel(chartModel)
    }

    private func setupUsageTrendChart() {
        let usageRecords = stockDataManager.allUsageRecords
        let last6Months = getLastSixMonths()
        let monthlyUsage = last6Months.map { month in
            usageRecords.filter { Calendar.current.isDate($0.usageDate, equalTo: month, toGranularity: .month) }
                .reduce(0.0) { $0 + $1.quantityUsed }
        }

        let monthNames = last6Months.map { month in
            let formatter = DateFormatter()
            formatter.dateFormat = "MMM"
            return formatter.string(from: month)
        }

        let chartModel = AAChartModel()
            .chartType(.areaspline)
            .title("Usage Trend")
            .subtitle("Leather consumption over time")
            .dataLabelsEnabled(false)
            .yAxisTitle("Quantity Used (sq ft)")
            .categories(monthNames)
            .series([
                AASeriesElement()
                    .name("Usage")
                    .data(monthlyUsage)
                    .color("#17a2b8")
                    .fillOpacity(0.3)
            ])

        usageTrendChartView.aa_drawChartWithChartModel(chartModel)
    }

    private func setupSupplierDistributionChart(statistics: StockStatistics) {
        let supplierData = statistics.supplierCounts.map { (supplier, count) in
            [supplier.isEmpty ? "Unknown" : supplier, count]
        }

        let chartModel = AAChartModel()
            .chartType(.area)
            .title("Supplier Distribution")
            .subtitle("Stock entries by supplier")
            .dataLabelsEnabled(true)
            .series([
                AASeriesElement()
                    .name("Suppliers")
                    .data(supplierData)
                    .colorByPoint(true)
            ])
            .colors(["#6f42c1", "#e83e8c", "#fd7e14", "#20c997", "#6610f2"])

        supplierDistributionChartView.aa_drawChartWithChartModel(chartModel)
    }

    private func setupMonthlyUsageChart() {
        let usageRecords = stockDataManager.allUsageRecords
        let categories = Constants.Stock.usageCategories
        let categoryUsage = categories.map { category in
            usageRecords.filter { $0.category == category }
                .reduce(0.0) { $0 + $1.quantityUsed }
        }

        let chartModel = AAChartModel()
            .chartType(.bar)
            .title("Usage by Category")
            .subtitle("Total consumption by project type")
            .dataLabelsEnabled(true)
            .yAxisTitle("Quantity Used (sq ft)")
            .categories(categories)
            .series([
                AASeriesElement()
                    .name("Usage")
                    .data(categoryUsage)
                    .color("#28a745")
            ])

        monthlyUsageChartView.aa_drawChartWithChartModel(chartModel)
    }

    private func getLastSixMonths() -> [Date] {
        let calendar = Calendar.current
        let now = Date()
        var months: [Date] = []

        for i in 0..<6 {
            if let month = calendar.date(byAdding: .month, value: -i, to: now) {
                months.insert(month, at: 0)
            }
        }

        return months
    }



    private func createStatCard(title: String, value: String, icon: String, color: UIColor) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = color.withAlphaComponent(0.1)
        cardView.setCornerRadius(Constants.CornerRadius.small)

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = Constants.Fonts.title2
        valueLabel.textColor = color
        valueLabel.textAlignment = .center

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.caption1
        titleLabel.textColor = .appTextSecondary
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 2

        cardView.addSubviews(iconImageView, valueLabel, titleLabel)

        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.small)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }

        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.tiny)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.tiny)
            make.bottom.equalToSuperview().inset(Constants.Spacing.small)
        }

        cardView.snp.makeConstraints { make in
            make.height.equalTo(100)
        }

        return cardView
    }

    // MARK: - Actions
    @objc private func doneButtonTapped() {
        dismiss(animated: true)
    }
}
