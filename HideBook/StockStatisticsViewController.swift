//
//  StockStatisticsViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class StockStatisticsViewController: UIViewController {
    
    // MARK: - Properties
    private let stockDataManager = StockDataManager.shared
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        populateData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05
        
        // Scroll View
        scrollView.showsVerticalScrollIndicator = false
        
        // Content View
        contentView.backgroundColor = .clear
        
        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Stock Statistics"
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(doneButtonTapped)
        )
    }
    
    private func populateData() {
        let statistics = stockDataManager.getStockStatistics()
        
        // Create statistics sections
        let sections = [
            createOverviewSection(statistics: statistics),
            createStockStatusSection(),
            createSuppliersSection(statistics: statistics)
        ]
        
        sections.forEach { contentView.addSubview($0) }
        
        // Setup constraints for sections
        for (index, section) in sections.enumerated() {
            section.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
                
                if index == 0 {
                    make.top.equalToSuperview().inset(Constants.Spacing.medium)
                } else {
                    make.top.equalTo(sections[index - 1].snp.bottom).offset(Constants.Spacing.medium)
                }
                
                if index == sections.count - 1 {
                    make.bottom.equalToSuperview().inset(Constants.Spacing.large)
                }
            }
        }
    }
    
    private func createOverviewSection(statistics: StockStatistics) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()
        
        let titleLabel = UILabel()
        titleLabel.text = "Overview"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary
        
        let statsStackView = UIStackView()
        statsStackView.axis = .horizontal
        statsStackView.spacing = Constants.Spacing.medium
        statsStackView.distribution = .fillEqually
        
        let totalEntriesCard = createStatCard(
            title: "Total Entries",
            value: "\(statistics.totalEntries)",
            icon: "cube.box",
            color: .appAccent
        )
        
        let totalValueCard = createStatCard(
            title: "Total Value",
            value: String(format: "$%.2f", statistics.totalValue),
            icon: "dollarsign.circle",
            color: .appSuccess
        )
        
        let totalQuantityCard = createStatCard(
            title: "Total Quantity",
            value: String(format: "%.1f", statistics.totalQuantity),
            icon: "scalemass",
            color: .appWarning
        )
        
        statsStackView.addArrangedSubview(totalEntriesCard)
        statsStackView.addArrangedSubview(totalValueCard)
        statsStackView.addArrangedSubview(totalQuantityCard)
        
        sectionView.addSubviews(titleLabel, statsStackView)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        return sectionView
    }
    
    private func createStockStatusSection() -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()
        
        let titleLabel = UILabel()
        titleLabel.text = "Stock Status"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary
        
        let lowStockCount = stockDataManager.lowStockEntries.count
        let outOfStockCount = stockDataManager.outOfStockEntries.count
        let inStockCount = stockDataManager.allStockEntries.count - lowStockCount - outOfStockCount
        
        let statusStackView = UIStackView()
        statusStackView.axis = .horizontal
        statusStackView.spacing = Constants.Spacing.medium
        statusStackView.distribution = .fillEqually
        
        let inStockCard = createStatCard(
            title: "In Stock",
            value: "\(inStockCount)",
            icon: "checkmark.circle",
            color: .appSuccess
        )
        
        let lowStockCard = createStatCard(
            title: "Low Stock",
            value: "\(lowStockCount)",
            icon: "exclamationmark.triangle",
            color: .appWarning
        )
        
        let outOfStockCard = createStatCard(
            title: "Out of Stock",
            value: "\(outOfStockCount)",
            icon: "xmark.circle",
            color: .appError
        )
        
        statusStackView.addArrangedSubview(inStockCard)
        statusStackView.addArrangedSubview(lowStockCard)
        statusStackView.addArrangedSubview(outOfStockCard)
        
        sectionView.addSubviews(titleLabel, statusStackView)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        statusStackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        return sectionView
    }
    
    private func createSuppliersSection(statistics: StockStatistics) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()
        
        let titleLabel = UILabel()
        titleLabel.text = "Suppliers"
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = Constants.Spacing.small
        stackView.alignment = .fill
        stackView.distribution = .fill
        
        if statistics.supplierCounts.isEmpty {
            let noDataLabel = UILabel()
            noDataLabel.text = "No supplier data available"
            noDataLabel.font = Constants.Fonts.body
            noDataLabel.textColor = .appTextTertiary
            noDataLabel.textAlignment = .center
            stackView.addArrangedSubview(noDataLabel)
        } else {
            for (supplier, count) in statistics.supplierCounts.sorted(by: { $0.value > $1.value }) {
                let supplierView = createSupplierRow(name: supplier.isEmpty ? "Unknown" : supplier, count: count)
                stackView.addArrangedSubview(supplierView)
            }
        }
        
        sectionView.addSubviews(titleLabel, stackView)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        return sectionView
    }
    
    private func createStatCard(title: String, value: String, icon: String, color: UIColor) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = color.withAlphaComponent(0.1)
        cardView.setCornerRadius(Constants.CornerRadius.small)
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = color
        iconImageView.contentMode = .scaleAspectFit
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = Constants.Fonts.title2
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.caption1
        titleLabel.textColor = .appTextSecondary
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 2
        
        cardView.addSubviews(iconImageView, valueLabel, titleLabel)
        
        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().inset(Constants.Spacing.small)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.tiny)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(Constants.Spacing.tiny)
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.tiny)
            make.bottom.equalToSuperview().inset(Constants.Spacing.small)
        }
        
        cardView.snp.makeConstraints { make in
            make.height.equalTo(100)
        }
        
        return cardView
    }
    
    private func createSupplierRow(name: String, count: Int) -> UIView {
        let rowView = UIView()
        
        let nameLabel = UILabel()
        nameLabel.text = name
        nameLabel.font = Constants.Fonts.body
        nameLabel.textColor = .appTextPrimary
        
        let countLabel = UILabel()
        countLabel.text = "\(count)"
        countLabel.font = Constants.Fonts.callout
        countLabel.textColor = .appAccent
        countLabel.textAlignment = .right
        
        rowView.addSubviews(nameLabel, countLabel)
        
        nameLabel.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            make.trailing.equalTo(countLabel.snp.leading).offset(-Constants.Spacing.small)
        }
        
        countLabel.snp.makeConstraints { make in
            make.trailing.top.bottom.equalToSuperview()
            make.width.equalTo(50)
        }
        
        rowView.snp.makeConstraints { make in
            make.height.equalTo(32)
        }
        
        return rowView
    }
    
    // MARK: - Actions
    @objc private func doneButtonTapped() {
        dismiss(animated: true)
    }
}
