//
//  LeatherArchiveViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class LeatherArchiveViewController: UIViewController {

    // MARK: - Properties
    private let dataManager = LeatherDataManager.shared
    private var leathers: [LeatherModel] = []
    private var filteredLeathers: [LeatherModel] = []
    private var isSearching = false
    private var currentSortType: LeatherSortType = .createdDateDescending

    // MARK: - UI Components
    private let gradientBackgroundView = GradientView()
    private let tableView = UITableView()
    private let searchController = UISearchController(searchResultsController: nil)
    private let emptyStateView = UIView()
    private let emptyImageView = UIImageView()
    private let emptyTitleLabel = UILabel()
    private let emptySubtitleLabel = UILabel()
    private let addButton = UIButton(type: .system)

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupSearchController()
        setupDataManager()
        loadData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        gradientBackgroundView.layoutSubviews()
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground

        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05

        // Table View
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(LeatherTableViewCell.self, forCellReuseIdentifier: LeatherTableViewCell.identifier)
        tableView.contentInset = UIEdgeInsets(top: Constants.Spacing.small, left: 0, bottom: Constants.Spacing.large, right: 0)

        // Empty State View
        setupEmptyStateView()

        // Add Button
        addButton.setImage(UIImage(systemName: "plus.circle.fill"), for: .normal)
        addButton.tintColor = .appAccent
        addButton.backgroundColor = .appCardBackground
        addButton.setCornerRadius(28)
        addButton.applyCardShadow()
        addButton.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)

        view.addSubviews(gradientBackgroundView, tableView, emptyStateView, addButton)
    }

    private func setupEmptyStateView() {
        emptyStateView.isHidden = true

        emptyImageView.image = UIImage(systemName: "folder")
        emptyImageView.tintColor = .systemGray3
        emptyImageView.contentMode = .scaleAspectFit

        emptyTitleLabel.text = "暂无皮革资料"
        emptyTitleLabel.font = Constants.Fonts.title2
        emptyTitleLabel.textColor = .appTextSecondary
        emptyTitleLabel.textAlignment = .center

        emptySubtitleLabel.text = "点击右下角的 + 按钮添加您的第一个皮革资料"
        emptySubtitleLabel.font = Constants.Fonts.body
        emptySubtitleLabel.textColor = .appTextTertiary
        emptySubtitleLabel.textAlignment = .center
        emptySubtitleLabel.numberOfLines = 0

        emptyStateView.addSubviews(emptyImageView, emptyTitleLabel, emptySubtitleLabel)
    }

    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }

        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(Constants.Spacing.large)
        }

        emptyImageView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }

        emptyTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyImageView.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.equalToSuperview()
        }

        emptySubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyTitleLabel.snp.bottom).offset(Constants.Spacing.small)
            make.leading.trailing.bottom.equalToSuperview()
        }

        addButton.snp.makeConstraints { make in
            make.trailing.equalTo(view.safeAreaLayoutGuide).inset(Constants.Spacing.medium)
            make.bottom.equalTo(view.safeAreaLayoutGuide).inset(Constants.Spacing.medium)
            make.width.height.equalTo(56)
        }
    }

    private func setupNavigationBar() {
        title = "Leather Archive"
        navigationController?.navigationBar.prefersLargeTitles = true

        // Sort button
        let sortButton = UIBarButtonItem(
            image: UIImage(systemName: "arrow.up.arrow.down"),
            style: .plain,
            target: self,
            action: #selector(sortButtonTapped)
        )

        // Filter button
        let filterButton = UIBarButtonItem(
            image: UIImage(systemName: "line.3.horizontal.decrease.circle"),
            style: .plain,
            target: self,
            action: #selector(filterButtonTapped)
        )

        navigationItem.rightBarButtonItems = [sortButton, filterButton]
    }

    private func setupSearchController() {
        searchController.searchResultsUpdater = self
        searchController.obscuresBackgroundDuringPresentation = false
        searchController.searchBar.placeholder = "Search leather archive..."
        searchController.searchBar.searchBarStyle = .minimal

        navigationItem.searchController = searchController
        definesPresentationContext = true
    }

    private func setupDataManager() {
        dataManager.delegate = self
    }

    // MARK: - Data Loading
    private func loadData() {
        leathers = dataManager.sortedLeathers(by: currentSortType)
        updateFilteredLeathers()
        updateEmptyState()
        tableView.reloadData()
    }

    private func updateFilteredLeathers() {
        if isSearching {
            let searchText = searchController.searchBar.text ?? ""
            filteredLeathers = dataManager.searchLeathers(with: searchText)
        } else {
            filteredLeathers = leathers
        }
    }

    private func updateEmptyState() {
        let isEmpty = filteredLeathers.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty

        if isEmpty && isSearching {
            emptyTitleLabel.text = "未找到相关皮革"
            emptySubtitleLabel.text = "尝试使用其他关键词搜索"
            emptyImageView.image = UIImage(systemName: "magnifyingglass")
        } else if isEmpty {
            emptyTitleLabel.text = "暂无皮革资料"
            emptySubtitleLabel.text = "点击右下角的 + 按钮添加您的第一个皮革资料"
            emptyImageView.image = UIImage(systemName: "folder")
        }
    }

    // MARK: - Actions
    @objc private func addButtonTapped() {
        let addViewController = AddLeatherViewController()
        let navigationController = UINavigationController(rootViewController: addViewController)
        present(navigationController, animated: true)
    }

    @objc private func sortButtonTapped() {
        showSortActionSheet()
    }

    @objc private func filterButtonTapped() {
        showFilterActionSheet()
    }

    // MARK: - Action Sheets
    private func showSortActionSheet() {
        let alertController = UIAlertController(title: "排序方式", message: nil, preferredStyle: .actionSheet)

        for sortType in LeatherSortType.allCases {
            let action = UIAlertAction(title: sortType.rawValue, style: .default) { _ in
                self.currentSortType = sortType
                self.loadData()
            }

            if sortType == currentSortType {
                action.setValue(true, forKey: "checked")
            }

            alertController.addAction(action)
        }

        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.first
        }

        present(alertController, animated: true)
    }

    private func showFilterActionSheet() {
        let alertController = UIAlertController(title: "筛选条件", message: nil, preferredStyle: .actionSheet)

        // Show favorites only
        alertController.addAction(UIAlertAction(title: "仅显示收藏", style: .default) { _ in
            self.leathers = self.dataManager.favoriteLeathers
            self.updateFilteredLeathers()
            self.updateEmptyState()
            self.tableView.reloadData()
        })

        // Show all
        alertController.addAction(UIAlertAction(title: "显示全部", style: .default) { _ in
            self.loadData()
        })

        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))

        if let popover = alertController.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.last
        }

        present(alertController, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension LeatherArchiveViewController: UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return filteredLeathers.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: LeatherTableViewCell.identifier, for: indexPath) as? LeatherTableViewCell else {
            return UITableViewCell()
        }

        let leather = filteredLeathers[indexPath.row]
        cell.configure(with: leather)
        cell.delegate = self

        return cell
    }
}

// MARK: - UITableViewDelegate
extension LeatherArchiveViewController: UITableViewDelegate {

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let leather = filteredLeathers[indexPath.row]
        let detailViewController = LeatherDetailViewController(leather: leather)
        navigationController?.pushViewController(detailViewController, animated: true)
    }

    func tableView(_ tableView: UITableView, trailingSwipeActionsConfigurationForRowAt indexPath: IndexPath) -> UISwipeActionsConfiguration? {
        let leather = filteredLeathers[indexPath.row]

        // Delete action
        let deleteAction = UIContextualAction(style: .destructive, title: "删除") { _, _, completion in
            self.showDeleteConfirmation(for: leather, at: indexPath)
            completion(true)
        }
        deleteAction.image = UIImage(systemName: "trash")

        // Duplicate action
        let duplicateAction = UIContextualAction(style: .normal, title: "复制") { _, _, completion in
            self.duplicateLeather(leather)
            completion(true)
        }
        duplicateAction.image = UIImage(systemName: "doc.on.doc")
        duplicateAction.backgroundColor = .systemBlue

        // Edit action
        let editAction = UIContextualAction(style: .normal, title: "编辑") { _, _, completion in
            self.editLeather(leather)
            completion(true)
        }
        editAction.image = UIImage(systemName: "pencil")
        editAction.backgroundColor = .systemOrange

        return UISwipeActionsConfiguration(actions: [deleteAction, duplicateAction, editAction])
    }

    // MARK: - Swipe Actions
    private func showDeleteConfirmation(for leather: LeatherModel, at indexPath: IndexPath) {
        let alertController = UIAlertController(
            title: "删除皮革资料",
            message: "确定要删除「\(leather.displayName)」吗？此操作无法撤销。",
            preferredStyle: .alert
        )

        alertController.addAction(UIAlertAction(title: "取消", style: .cancel))
        alertController.addAction(UIAlertAction(title: "删除", style: .destructive) { _ in
            self.deleteLeather(leather, at: indexPath)
        })

        present(alertController, animated: true)
    }

    private func deleteLeather(_ leather: LeatherModel, at indexPath: IndexPath) {
        dataManager.deleteLeather(withId: leather.id)

        // Animate removal
        filteredLeathers.remove(at: indexPath.row)
        tableView.deleteRows(at: [indexPath], with: .fade)

        // Update empty state
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.updateEmptyState()
        }
    }

    private func duplicateLeather(_ leather: LeatherModel) {
        guard let duplicatedLeather = dataManager.duplicateLeather(withId: leather.id) else { return }

        // Show success message
        let alertController = UIAlertController(
            title: "复制成功",
            message: "已创建「\(duplicatedLeather.displayName)」的副本",
            preferredStyle: .alert
        )
        alertController.addAction(UIAlertAction(title: "确定", style: .default))
        present(alertController, animated: true)
    }

    private func editLeather(_ leather: LeatherModel) {
        let editViewController = AddLeatherViewController(leather: leather)
        let navigationController = UINavigationController(rootViewController: editViewController)
        present(navigationController, animated: true)
    }
}

// MARK: - UISearchResultsUpdating
extension LeatherArchiveViewController: UISearchResultsUpdating {

    func updateSearchResults(for searchController: UISearchController) {
        let searchText = searchController.searchBar.text ?? ""
        isSearching = !searchText.isEmpty

        updateFilteredLeathers()
        updateEmptyState()
        tableView.reloadData()
    }
}

// MARK: - LeatherTableViewCellDelegate
extension LeatherArchiveViewController: LeatherTableViewCellDelegate {

    func leatherCellDidTapFavorite(_ cell: LeatherTableViewCell, leather: LeatherModel) {
        var updatedLeather = leather
        updatedLeather.toggleFavorite()
        dataManager.updateLeather(updatedLeather)

        // Update local data
        if let index = filteredLeathers.firstIndex(where: { $0.id == leather.id }) {
            filteredLeathers[index] = updatedLeather
        }

        if let index = leathers.firstIndex(where: { $0.id == leather.id }) {
            leathers[index] = updatedLeather
        }
    }
}

// MARK: - LeatherDataManagerDelegate
extension LeatherArchiveViewController: LeatherDataManagerDelegate {

    func dataManagerDidUpdateLeathers(_ manager: LeatherDataManager) {
        DispatchQueue.main.async {
            self.loadData()
        }
    }

    func dataManager(_ manager: LeatherDataManager, didFailWithError error: Error) {
        DispatchQueue.main.async {
            let alertController = UIAlertController(
                title: "错误",
                message: "数据操作失败：\(error.localizedDescription)",
                preferredStyle: .alert
            )
            alertController.addAction(UIAlertAction(title: "确定", style: .default))
            self.present(alertController, animated: true)
        }
    }
}
