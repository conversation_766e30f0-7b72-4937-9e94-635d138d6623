//
//  LeatherTableViewCell.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

protocol LeatherTableViewCellDelegate: AnyObject {
    func leatherCellDidTapFavorite(_ cell: LeatherTableViewCell, leather: LeatherModel)
}

class LeatherTableViewCell: UITableViewCell {
    
    // MARK: - Properties
    static let identifier = "LeatherTableViewCell"
    weak var delegate: LeatherTableViewCellDelegate?
    
    private var leather: LeatherModel?
    
    // MARK: - UI Components
    private let containerView = UIView()
    private let gradientView = GradientView()
    private let leatherImageView = UIImageView()
    private let nameLabel = UILabel()
    private let materialLabel = UILabel()
    private let thicknessLabel = UILabel()
    private let priceLabel = UILabel()
    private let favoriteButton = UIButton(type: .system)
    private let tagsStackView = UIStackView()
    private let detailStackView = UIStackView()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func prepareForReuse() {
        super.prepareForReuse()
        leatherImageView.image = nil
        nameLabel.text = nil
        materialLabel.text = nil
        thicknessLabel.text = nil
        priceLabel.text = nil
        favoriteButton.isSelected = false
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        leather = nil
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        gradientView.layoutSubviews()
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        // Container View
        containerView.backgroundColor = .appCardBackground
        containerView.setCornerRadius(Constants.CornerRadius.medium)
        containerView.applyCardShadow()
        
        // Gradient View
        gradientView.setPrimaryGradient()
        gradientView.setCornerRadius(Constants.CornerRadius.medium)
        gradientView.alpha = 0.1
        
        // Leather Image View
        leatherImageView.contentMode = .scaleAspectFill
        leatherImageView.setCornerRadius(Constants.CornerRadius.small)
        leatherImageView.backgroundColor = .systemGray5
        leatherImageView.image = UIImage(systemName: "photo")
        leatherImageView.tintColor = .systemGray3
        
        // Name Label
        nameLabel.font = Constants.Fonts.headline
        nameLabel.textColor = .appTextPrimary
        nameLabel.numberOfLines = 2
        
        // Material Label
        materialLabel.font = Constants.Fonts.subheadline
        materialLabel.textColor = .appTextSecondary
        
        // Thickness Label
        thicknessLabel.font = Constants.Fonts.caption1
        thicknessLabel.textColor = .appTextTertiary
        
        // Price Label
        priceLabel.font = Constants.Fonts.callout
        priceLabel.textColor = .appAccent
        priceLabel.textAlignment = .right
        
        // Favorite Button
        favoriteButton.setImage(UIImage(systemName: "heart"), for: .normal)
        favoriteButton.setImage(UIImage(systemName: "heart.fill"), for: .selected)
        favoriteButton.tintColor = .appError
        favoriteButton.addTarget(self, action: #selector(favoriteButtonTapped), for: .touchUpInside)
        
        // Tags Stack View
        tagsStackView.axis = .horizontal
        tagsStackView.spacing = Constants.Spacing.tiny
        tagsStackView.alignment = .leading
        tagsStackView.distribution = .fillProportionally
        
        // Detail Stack View
        detailStackView.axis = .vertical
        detailStackView.spacing = Constants.Spacing.tiny
        detailStackView.alignment = .leading
        detailStackView.distribution = .fill
        
        // Add subviews
        contentView.addSubview(containerView)
        containerView.addSubviews(gradientView, leatherImageView, nameLabel, detailStackView, priceLabel, favoriteButton, tagsStackView)
        detailStackView.addArrangedSubview(materialLabel)
        detailStackView.addArrangedSubview(thicknessLabel)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(Constants.Spacing.small)
        }
        
        gradientView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        leatherImageView.snp.makeConstraints { make in
            make.leading.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.height.equalTo(60)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.leading.equalTo(leatherImageView.snp.trailing).offset(Constants.Spacing.medium)
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.trailing.equalTo(favoriteButton.snp.leading).offset(-Constants.Spacing.small)
        }
        
        detailStackView.snp.makeConstraints { make in
            make.leading.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(Constants.Spacing.tiny)
            make.trailing.equalTo(priceLabel.snp.leading).offset(-Constants.Spacing.small)
        }
        
        favoriteButton.snp.makeConstraints { make in
            make.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.top.equalToSuperview().inset(Constants.Spacing.medium)
            make.width.height.equalTo(24)
        }
        
        priceLabel.snp.makeConstraints { make in
            make.trailing.equalTo(favoriteButton.snp.leading).offset(-Constants.Spacing.small)
            make.centerY.equalTo(detailStackView)
            make.width.greaterThanOrEqualTo(80)
        }
        
        tagsStackView.snp.makeConstraints { make in
            make.leading.equalTo(nameLabel)
            make.top.equalTo(detailStackView.snp.bottom).offset(Constants.Spacing.small)
            make.trailing.equalToSuperview().inset(Constants.Spacing.medium)
            make.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
    }
    
    // MARK: - Configuration
    func configure(with leather: LeatherModel) {
        self.leather = leather
        
        nameLabel.text = leather.displayName
        materialLabel.text = "\(leather.materialSource) • \(leather.tanningMethod)"
        thicknessLabel.text = leather.thicknessString
        priceLabel.text = leather.priceString
        favoriteButton.isSelected = leather.isFavorite
        
        // Configure image
        if let imageData = leather.imageData, let image = UIImage(data: imageData) {
            leatherImageView.image = image
            leatherImageView.contentMode = .scaleAspectFill
        } else {
            leatherImageView.image = UIImage(systemName: "photo")
            leatherImageView.contentMode = .center
        }
        
        // Configure tags
        setupTags(leather.tags)
        
        // Animate appearance
        containerView.scaleIn()
    }
    
    private func setupTags(_ tags: [String]) {
        tagsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        let maxTags = 3
        let displayTags = Array(tags.prefix(maxTags))
        
        for tag in displayTags {
            let tagLabel = createTagLabel(text: tag)
            tagsStackView.addArrangedSubview(tagLabel)
        }
        
        if tags.count > maxTags {
            let moreLabel = createTagLabel(text: "+\(tags.count - maxTags)")
            moreLabel.backgroundColor = .systemGray4
            tagsStackView.addArrangedSubview(moreLabel)
        }
    }
    
    private func createTagLabel(text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = Constants.Fonts.caption2
        label.textColor = .white
        label.backgroundColor = .appAccent
        label.textAlignment = .center
        label.setCornerRadius(Constants.CornerRadius.small / 2)
        label.snp.makeConstraints { make in
            make.height.equalTo(20)
            make.width.greaterThanOrEqualTo(30)
        }
        return label
    }
    
    // MARK: - Actions
    @objc private func favoriteButtonTapped() {
        guard let leather = leather else { return }
        
        favoriteButton.isSelected.toggle()
        
        // Add animation
        UIView.animate(withDuration: 0.2, delay: 0, usingSpringWithDamping: 0.6, initialSpringVelocity: 0.8, options: .curveEaseInOut) {
            self.favoriteButton.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        } completion: { _ in
            UIView.animate(withDuration: 0.1) {
                self.favoriteButton.transform = CGAffineTransform.identity
            }
        }
        
        delegate?.leatherCellDidTapFavorite(self, leather: leather)
    }
}
