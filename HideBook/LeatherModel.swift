//
//  LeatherModel.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

// MARK: - Leather Model
struct LeatherModel: Codable, Equatable {
    
    // MARK: - Properties
    let id: String
    var name: String
    var materialSource: String // 材质来源
    var tanningMethod: String // 鞣制方式
    var surfaceTreatment: String // 表面处理
    var thickness: Double // 厚度 (mm)
    var colorCode: String // 色号
    var colorDescription: String // 颜色描述
    var origin: String // 产地
    var supplier: String // 供应商
    var brand: String // 品牌
    var textureDescription: String // 手感描述
    var usageSuggestion: String // 使用建议
    var imageName: String? // 图片名称
    var imageData: Data? // 图片数据
    var createdAt: Date
    var updatedAt: Date
    var tags: [String] // 标签
    var notes: String // 备注
    var price: Double? // 价格
    var priceUnit: String? // 价格单位
    var isFavorite: Bool // 是否收藏
    
    // MARK: - Initialization
    init(
        id: String = UUID().uuidString,
        name: String,
        materialSource: String,
        tanningMethod: String,
        surfaceTreatment: String,
        thickness: Double,
        colorCode: String = "",
        colorDescription: String = "",
        origin: String = "",
        supplier: String = "",
        brand: String = "",
        textureDescription: String = "",
        usageSuggestion: String = "",
        imageName: String? = nil,
        imageData: Data? = nil,
        tags: [String] = [],
        notes: String = "",
        price: Double? = nil,
        priceUnit: String? = nil,
        isFavorite: Bool = false
    ) {
        self.id = id
        self.name = name
        self.materialSource = materialSource
        self.tanningMethod = tanningMethod
        self.surfaceTreatment = surfaceTreatment
        self.thickness = thickness
        self.colorCode = colorCode
        self.colorDescription = colorDescription
        self.origin = origin
        self.supplier = supplier
        self.brand = brand
        self.textureDescription = textureDescription
        self.usageSuggestion = usageSuggestion
        self.imageName = imageName
        self.imageData = imageData
        self.createdAt = Date()
        self.updatedAt = Date()
        self.tags = tags
        self.notes = notes
        self.price = price
        self.priceUnit = priceUnit
        self.isFavorite = isFavorite
    }
    
    // MARK: - Computed Properties
    var displayName: String {
        return name.isEmpty ? "未命名皮革" : name
    }
    
    var thicknessString: String {
        return String(format: "%.1f mm", thickness)
    }
    
    var priceString: String {
        guard let price = price else { return "价格未设定" }
        let unit = priceUnit ?? "元"
        return String(format: "%.2f %@", price, unit)
    }
    
    var formattedCreatedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: createdAt)
    }
    
    var formattedUpdatedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: updatedAt)
    }
    
    // MARK: - Methods
    mutating func updateTimestamp() {
        updatedAt = Date()
    }
    
    mutating func toggleFavorite() {
        isFavorite.toggle()
        updateTimestamp()
    }
    
    mutating func addTag(_ tag: String) {
        if !tags.contains(tag) {
            tags.append(tag)
            updateTimestamp()
        }
    }
    
    mutating func removeTag(_ tag: String) {
        tags.removeAll { $0 == tag }
        updateTimestamp()
    }
    
    func hasTag(_ tag: String) -> Bool {
        return tags.contains(tag)
    }
    
    // MARK: - Search & Filter
    func matchesSearchText(_ searchText: String) -> Bool {
        let lowercasedSearch = searchText.lowercased()
        return name.lowercased().contains(lowercasedSearch) ||
               materialSource.lowercased().contains(lowercasedSearch) ||
               tanningMethod.lowercased().contains(lowercasedSearch) ||
               surfaceTreatment.lowercased().contains(lowercasedSearch) ||
               colorDescription.lowercased().contains(lowercasedSearch) ||
               origin.lowercased().contains(lowercasedSearch) ||
               supplier.lowercased().contains(lowercasedSearch) ||
               brand.lowercased().contains(lowercasedSearch) ||
               textureDescription.lowercased().contains(lowercasedSearch) ||
               usageSuggestion.lowercased().contains(lowercasedSearch) ||
               tags.contains { $0.lowercased().contains(lowercasedSearch) } ||
               notes.lowercased().contains(lowercasedSearch)
    }
    
    // MARK: - Validation
    var isValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !materialSource.isEmpty &&
               !tanningMethod.isEmpty &&
               !surfaceTreatment.isEmpty &&
               thickness > 0
    }
    
    var validationErrors: [String] {
        var errors: [String] = []
        
        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("皮革名称不能为空")
        }
        
        if materialSource.isEmpty {
            errors.append("请选择材质来源")
        }
        
        if tanningMethod.isEmpty {
            errors.append("请选择鞣制方式")
        }
        
        if surfaceTreatment.isEmpty {
            errors.append("请选择表面处理")
        }
        
        if thickness <= 0 {
            errors.append("厚度必须大于0")
        }
        
        if thickness > Constants.Leather.thicknessRange.max {
            errors.append("厚度不能超过\(Constants.Leather.thicknessRange.max)mm")
        }
        
        return errors
    }
}

// MARK: - Sample Data
extension LeatherModel {
    static func sampleData() -> [LeatherModel] {
        return [
            LeatherModel(
                name: "意大利植鞣革",
                materialSource: "牛皮",
                tanningMethod: "植鞣",
                surfaceTreatment: "原色",
                thickness: 2.0,
                colorCode: "#8B4513",
                colorDescription: "自然棕色",
                origin: "意大利",
                supplier: "Conceria Santa Croce",
                brand: "Buttero",
                textureDescription: "硬挺",
                usageSuggestion: "适合制作皮带、钱包等需要定型的产品",
                tags: ["植鞣", "意大利", "牛皮", "硬挺"],
                notes: "经典的意大利植鞣革，质量上乘",
                price: 120.0,
                priceUnit: "元/平方尺",
                isFavorite: true
            ),
            LeatherModel(
                name: "日本马臀皮",
                materialSource: "马臀皮",
                tanningMethod: "植鞣",
                surfaceTreatment: "光面",
                thickness: 1.8,
                colorCode: "#654321",
                colorDescription: "深棕色",
                origin: "日本",
                supplier: "新喜皮革",
                brand: "Shinki",
                textureDescription: "柔软",
                usageSuggestion: "适合制作高端皮鞋、手袋",
                tags: ["马臀皮", "日本", "高端", "柔软"],
                notes: "世界顶级的马臀皮，光泽度极佳",
                price: 800.0,
                priceUnit: "元/平方尺",
                isFavorite: true
            ),
            LeatherModel(
                name: "法国小羊皮",
                materialSource: "羊皮",
                tanningMethod: "铬鞣",
                surfaceTreatment: "磨砂",
                thickness: 0.8,
                colorCode: "#F5F5DC",
                colorDescription: "米白色",
                origin: "法国",
                supplier: "Haas Tannery",
                brand: "Haas",
                textureDescription: "柔软",
                usageSuggestion: "适合制作手套、内衬",
                tags: ["羊皮", "法国", "柔软", "磨砂"],
                notes: "触感极佳的小羊皮，适合精细工艺",
                price: 200.0,
                priceUnit: "元/平方尺"
            )
        ]
    }
}
