//
//  LeatherModel.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

// MARK: - Leather Model
struct LeatherModel: Codable, Equatable {

    // MARK: - Properties
    let id: String
    var name: String
    var materialSource: String // 材质来源
    var tanningMethod: String // 鞣制方式
    var surfaceTreatment: String // 表面处理
    var thickness: Double // 厚度 (mm)
    var colorCode: String // 色号
    var colorDescription: String // 颜色描述
    var origin: String // 产地
    var supplier: String // 供应商
    var brand: String // 品牌
    var textureDescription: String // 手感描述
    var usageSuggestion: String // 使用建议
    var imageName: String? // 图片名称
    var imageData: Data? // 图片数据
    var createdAt: Date
    var updatedAt: Date
    var tags: [String] // 标签
    var notes: String // 备注
    var price: Double? // 价格
    var priceUnit: String? // 价格单位
    var isFavorite: Bool // 是否收藏

    // MARK: - Initialization
    init(
        id: String = UUID().uuidString,
        name: String,
        materialSource: String,
        tanningMethod: String,
        surfaceTreatment: String,
        thickness: Double,
        colorCode: String = "",
        colorDescription: String = "",
        origin: String = "",
        supplier: String = "",
        brand: String = "",
        textureDescription: String = "",
        usageSuggestion: String = "",
        imageName: String? = nil,
        imageData: Data? = nil,
        tags: [String] = [],
        notes: String = "",
        price: Double? = nil,
        priceUnit: String? = nil,
        isFavorite: Bool = false
    ) {
        self.id = id
        self.name = name
        self.materialSource = materialSource
        self.tanningMethod = tanningMethod
        self.surfaceTreatment = surfaceTreatment
        self.thickness = thickness
        self.colorCode = colorCode
        self.colorDescription = colorDescription
        self.origin = origin
        self.supplier = supplier
        self.brand = brand
        self.textureDescription = textureDescription
        self.usageSuggestion = usageSuggestion
        self.imageName = imageName
        self.imageData = imageData
        self.createdAt = Date()
        self.updatedAt = Date()
        self.tags = tags
        self.notes = notes
        self.price = price
        self.priceUnit = priceUnit
        self.isFavorite = isFavorite
    }

    // MARK: - Computed Properties
    var displayName: String {
        return name.isEmpty ? "Unnamed Leather" : name
    }

    var thicknessString: String {
        return String(format: "%.1f mm", thickness)
    }

    var priceString: String {
        guard let price = price else { return "Price not set" }
        let unit = priceUnit ?? "$"
        return String(format: "%.2f %@", price, unit)
    }

    var formattedCreatedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: createdAt)
    }

    var formattedUpdatedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "en_US")
        return formatter.string(from: updatedAt)
    }

    // MARK: - Methods
    mutating func updateTimestamp() {
        updatedAt = Date()
    }

    mutating func toggleFavorite() {
        isFavorite.toggle()
        updateTimestamp()
    }

    mutating func addTag(_ tag: String) {
        if !tags.contains(tag) {
            tags.append(tag)
            updateTimestamp()
        }
    }

    mutating func removeTag(_ tag: String) {
        tags.removeAll { $0 == tag }
        updateTimestamp()
    }

    func hasTag(_ tag: String) -> Bool {
        return tags.contains(tag)
    }

    // MARK: - Search & Filter
    func matchesSearchText(_ searchText: String) -> Bool {
        let lowercasedSearch = searchText.lowercased()
        return name.lowercased().contains(lowercasedSearch) ||
               materialSource.lowercased().contains(lowercasedSearch) ||
               tanningMethod.lowercased().contains(lowercasedSearch) ||
               surfaceTreatment.lowercased().contains(lowercasedSearch) ||
               colorDescription.lowercased().contains(lowercasedSearch) ||
               origin.lowercased().contains(lowercasedSearch) ||
               supplier.lowercased().contains(lowercasedSearch) ||
               brand.lowercased().contains(lowercasedSearch) ||
               textureDescription.lowercased().contains(lowercasedSearch) ||
               usageSuggestion.lowercased().contains(lowercasedSearch) ||
               tags.contains { $0.lowercased().contains(lowercasedSearch) } ||
               notes.lowercased().contains(lowercasedSearch)
    }

    // MARK: - Validation
    var isValid: Bool {
        return !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               !materialSource.isEmpty &&
               !tanningMethod.isEmpty &&
               !surfaceTreatment.isEmpty &&
               thickness > 0
    }

    var validationErrors: [String] {
        var errors: [String] = []

        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            errors.append("Leather name cannot be empty")
        }

        if materialSource.isEmpty {
            errors.append("Please select material source")
        }

        if tanningMethod.isEmpty {
            errors.append("Please select tanning method")
        }

        if surfaceTreatment.isEmpty {
            errors.append("Please select surface treatment")
        }

        if thickness <= 0 {
            errors.append("Thickness must be greater than 0")
        }

        if thickness > Constants.Leather.thicknessRange.max {
            errors.append("Thickness cannot exceed \(Constants.Leather.thicknessRange.max)mm")
        }

        return errors
    }
}

// MARK: - Sample Data
extension LeatherModel {
    static func sampleData() -> [LeatherModel] {
        return [
            LeatherModel(
                name: "Italian Vegetable Tanned Leather",
                materialSource: "Cowhide",
                tanningMethod: "Vegetable Tanned",
                surfaceTreatment: "Natural",
                thickness: 2.0,
                colorCode: "#8B4513",
                colorDescription: "Natural Brown",
                origin: "Italy",
                supplier: "Conceria Santa Croce",
                brand: "Buttero",
                textureDescription: "Firm",
                usageSuggestion: "Perfect for belts, wallets and structured goods",
                tags: ["Vegetable Tanned", "Italy", "Cowhide", "Firm"],
                notes: "Classic Italian vegetable tanned leather, premium quality",
                price: 12.50,
                priceUnit: "$/sq ft",
                isFavorite: true
            ),
            LeatherModel(
                name: "Japanese Shell Cordovan",
                materialSource: "Shell Cordovan",
                tanningMethod: "Vegetable Tanned",
                surfaceTreatment: "Smooth",
                thickness: 1.8,
                colorCode: "#654321",
                colorDescription: "Dark Brown",
                origin: "Japan",
                supplier: "Shinki Leather",
                brand: "Shinki",
                textureDescription: "Soft",
                usageSuggestion: "Ideal for premium shoes and handbags",
                tags: ["Shell Cordovan", "Japan", "Premium", "Soft"],
                notes: "World-class shell cordovan with exceptional luster",
                price: 85.00,
                priceUnit: "$/sq ft",
                isFavorite: true
            ),
            LeatherModel(
                name: "French Lambskin",
                materialSource: "Sheepskin",
                tanningMethod: "Chrome Tanned",
                surfaceTreatment: "Nubuck",
                thickness: 0.8,
                colorCode: "#F5F5DC",
                colorDescription: "Cream White",
                origin: "France",
                supplier: "Haas Tannery",
                brand: "Haas",
                textureDescription: "Soft",
                usageSuggestion: "Perfect for gloves and linings",
                tags: ["Sheepskin", "France", "Soft", "Nubuck"],
                notes: "Exceptional hand feel lambskin, ideal for fine craftsmanship",
                price: 25.00,
                priceUnit: "$/sq ft"
            )
        ]
    }
}
