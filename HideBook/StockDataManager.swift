//
//  StockDataManager.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

protocol StockDataManagerDelegate: AnyObject {
    func dataManagerDidUpdateStock(_ manager: StockDataManager)
    func dataManager(_ manager: StockDataManager, didFailWithError error: Error)
}

class StockDataManager {

    // MARK: - Singleton
    static let shared = StockDataManager()

    // MARK: - Properties
    weak var delegate: StockDataManagerDelegate?

    private var stockEntries: [StockEntry] = []
    private var usageRecords: [UsageRecord] = []
    private let userDefaults = UserDefaults.standard
    private let stockEntriesKey = "SavedStockEntries"
    private let usageRecordsKey = "SavedUsageRecords"

    // MARK: - Computed Properties
    var allStockEntries: [StockEntry] {
        return stockEntries
    }

    var allUsageRecords: [UsageRecord] {
        return usageRecords
    }

    var lowStockEntries: [StockEntry] {
        return stockEntries.filter { $0.isLowStock }
    }

    var outOfStockEntries: [StockEntry] {
        return stockEntries.filter { $0.isOutOfStock }
    }

    var stockEntriesCount: Int {
        return stockEntries.count
    }

    var usageRecordsCount: Int {
        return usageRecords.count
    }

    // MARK: - Initialization
    private init() {
        loadData()

        // Load sample data if empty
        if stockEntries.isEmpty {
            loadSampleData()
        }
    }

    // MARK: - Data Persistence
    private func saveData() {
        do {
            let stockData = try JSONEncoder().encode(stockEntries)
            let usageData = try JSONEncoder().encode(usageRecords)

            userDefaults.set(stockData, forKey: stockEntriesKey)
            userDefaults.set(usageData, forKey: usageRecordsKey)
            userDefaults.synchronize()

            DispatchQueue.main.async {
                self.delegate?.dataManagerDidUpdateStock(self)
            }
        } catch {
            print("Failed to save stock data: \(error)")
            DispatchQueue.main.async {
                self.delegate?.dataManager(self, didFailWithError: error)
            }
        }
    }

    private func loadData() {
        // Load stock entries
        if let stockData = userDefaults.data(forKey: stockEntriesKey) {
            do {
                stockEntries = try JSONDecoder().decode([StockEntry].self, from: stockData)
            } catch {
                print("Failed to load stock entries: \(error)")
                stockEntries = []
            }
        }

        // Load usage records
        if let usageData = userDefaults.data(forKey: usageRecordsKey) {
            do {
                usageRecords = try JSONDecoder().decode([UsageRecord].self, from: usageData)
            } catch {
                print("Failed to load usage records: \(error)")
                usageRecords = []
            }
        }
    }

    private func loadSampleData() {
        // Create sample stock entries without circular dependency
        let sampleStockEntries = createSampleStockEntries()
        stockEntries = sampleStockEntries

        // Create sample usage records
        let sampleUsageRecords = createSampleUsageRecords()
        usageRecords = sampleUsageRecords

        saveData()
    }

    private func createSampleStockEntries() -> [StockEntry] {
        // Create sample entries with dummy leather IDs
        return [
            StockEntry(
                leatherId: "sample-leather-1",
                currentQuantity: 25.5,
                areaUnit: "sq ft",
                unitPrice: 12.50,
                currency: "$",
                supplier: "Conceria Santa Croce",
                batchNumber: "VSC-2024-001",
                purchaseDate: Calendar.current.date(byAdding: .day, value: -30, to: Date()) ?? Date(),
                lowStockThreshold: 5.0,
                notes: "High quality vegetable tanned leather"
            ),
            StockEntry(
                leatherId: "sample-leather-2",
                currentQuantity: 8.2,
                areaUnit: "sq ft",
                unitPrice: 45.00,
                currency: "$",
                supplier: "Shinki Leather",
                batchNumber: "SHK-2024-002",
                purchaseDate: Calendar.current.date(byAdding: .day, value: -15, to: Date()) ?? Date(),
                lowStockThreshold: 10.0,
                notes: "Premium shell cordovan"
            )
        ]
    }

    private func createSampleUsageRecords() -> [UsageRecord] {
        guard !stockEntries.isEmpty else { return [] }

        return [
            UsageRecord(
                stockId: stockEntries[0].id,
                projectName: "Bifold Wallet",
                category: "Wallet",
                quantityUsed: 2.5,
                remainingAfterUse: 23.0,
                usageDate: Calendar.current.date(byAdding: .day, value: -5, to: Date()) ?? Date(),
                notes: "Client commission - brown wallet"
            ),
            UsageRecord(
                stockId: stockEntries[0].id,
                projectName: "Card Holder",
                category: "Wallet",
                quantityUsed: 1.2,
                remainingAfterUse: 21.8,
                usageDate: Calendar.current.date(byAdding: .day, value: -2, to: Date()) ?? Date(),
                notes: "Personal use"
            )
        ]
    }

    // MARK: - Stock Entry CRUD Operations
    func addStockEntry(_ stockEntry: StockEntry) {
        stockEntries.append(stockEntry)
        saveData()
    }

    func updateStockEntry(_ stockEntry: StockEntry) {
        guard let index = stockEntries.firstIndex(where: { $0.id == stockEntry.id }) else {
            return
        }

        var updatedEntry = stockEntry
        updatedEntry.updateTimestamp()
        stockEntries[index] = updatedEntry
        saveData()
    }

    func deleteStockEntry(withId id: String) {
        stockEntries.removeAll { $0.id == id }
        // Also remove related usage records
        usageRecords.removeAll { $0.stockId == id }
        saveData()
    }

    func deleteStockEntry(at index: Int) {
        guard index >= 0 && index < stockEntries.count else { return }
        let stockId = stockEntries[index].id
        stockEntries.remove(at: index)
        // Also remove related usage records
        usageRecords.removeAll { $0.stockId == stockId }
        saveData()
    }

    func getStockEntry(withId id: String) -> StockEntry? {
        return stockEntries.first { $0.id == id }
    }

    func getStockEntries(forLeatherId leatherId: String) -> [StockEntry] {
        return stockEntries.filter { $0.leatherId == leatherId }
    }

    // MARK: - Usage Record CRUD Operations
    func addUsageRecord(_ usageRecord: UsageRecord) {
        usageRecords.append(usageRecord)

        // Update stock quantity
        if let stockIndex = stockEntries.firstIndex(where: { $0.id == usageRecord.stockId }) {
            stockEntries[stockIndex].updateQuantity(usageRecord.remainingAfterUse)
        }

        saveData()
    }

    func updateUsageRecord(_ usageRecord: UsageRecord) {
        guard let index = usageRecords.firstIndex(where: { $0.id == usageRecord.id }) else {
            return
        }

        usageRecords[index] = usageRecord
        saveData()
    }

    func deleteUsageRecord(withId id: String) {
        usageRecords.removeAll { $0.id == id }
        saveData()
    }

    func deleteUsageRecord(at index: Int) {
        guard index >= 0 && index < usageRecords.count else { return }
        usageRecords.remove(at: index)
        saveData()
    }

    func getUsageRecord(withId id: String) -> UsageRecord? {
        return usageRecords.first { $0.id == id }
    }

    func getUsageRecords(forStockId stockId: String) -> [UsageRecord] {
        return usageRecords.filter { $0.stockId == stockId }
    }

    func getUsageRecords(forLeatherId leatherId: String) -> [UsageRecord] {
        let stockIds = stockEntries.filter { $0.leatherId == leatherId }.map { $0.id }
        return usageRecords.filter { stockIds.contains($0.stockId) }
    }

    // MARK: - Search & Filter
    func searchStockEntries(with searchText: String) -> [StockEntry] {
        guard !searchText.isEmpty else { return stockEntries }

        let lowercasedSearch = searchText.lowercased()
        return stockEntries.filter { stockEntry in
            return stockEntry.leatherName.lowercased().contains(lowercasedSearch) ||
                   stockEntry.supplier.lowercased().contains(lowercasedSearch) ||
                   stockEntry.batchNumber.lowercased().contains(lowercasedSearch) ||
                   stockEntry.notes.lowercased().contains(lowercasedSearch)
        }
    }

    func searchUsageRecords(with searchText: String) -> [UsageRecord] {
        guard !searchText.isEmpty else { return usageRecords }

        let lowercasedSearch = searchText.lowercased()
        return usageRecords.filter { usageRecord in
            return usageRecord.projectName.lowercased().contains(lowercasedSearch) ||
                   usageRecord.category.lowercased().contains(lowercasedSearch) ||
                   usageRecord.leatherName.lowercased().contains(lowercasedSearch) ||
                   usageRecord.notes.lowercased().contains(lowercasedSearch)
        }
    }

    func filterStockEntries(by status: String? = nil, supplier: String? = nil) -> [StockEntry] {
        return stockEntries.filter { stockEntry in
            if let status = status, stockEntry.stockStatus != status {
                return false
            }
            if let supplier = supplier, stockEntry.supplier != supplier {
                return false
            }
            return true
        }
    }

    // MARK: - Sorting
    func sortedStockEntries(by sortType: StockSortType) -> [StockEntry] {
        switch sortType {
        case .leatherNameAscending:
            return stockEntries.sorted { $0.leatherName.localizedCaseInsensitiveCompare($1.leatherName) == .orderedAscending }
        case .leatherNameDescending:
            return stockEntries.sorted { $0.leatherName.localizedCaseInsensitiveCompare($1.leatherName) == .orderedDescending }
        case .quantityAscending:
            return stockEntries.sorted { $0.currentQuantity < $1.currentQuantity }
        case .quantityDescending:
            return stockEntries.sorted { $0.currentQuantity > $1.currentQuantity }
        case .purchaseDateAscending:
            return stockEntries.sorted { $0.purchaseDate < $1.purchaseDate }
        case .purchaseDateDescending:
            return stockEntries.sorted { $0.purchaseDate > $1.purchaseDate }
        case .lowStockFirst:
            return stockEntries.sorted { $0.isLowStock && !$1.isLowStock }
        case .outOfStockFirst:
            return stockEntries.sorted { $0.isOutOfStock && !$1.isOutOfStock }
        }
    }

    // MARK: - Statistics
    func getStockStatistics() -> StockStatistics {
        let totalEntries = stockEntries.count
        let lowStockCount = lowStockEntries.count
        let outOfStockCount = outOfStockEntries.count
        let totalValue = stockEntries.reduce(0) { $0 + $1.totalCost }
        let totalQuantity = stockEntries.reduce(0) { $0 + $1.currentQuantity }

        let supplierCounts = Dictionary(grouping: stockEntries, by: { $0.supplier })
            .mapValues { $0.count }

        return StockStatistics(
            totalEntries: totalEntries,
            lowStockCount: lowStockCount,
            outOfStockCount: outOfStockCount,
            totalValue: totalValue,
            totalQuantity: totalQuantity,
            supplierCounts: supplierCounts
        )
    }
}

// MARK: - Supporting Types
enum StockSortType: String, CaseIterable {
    case leatherNameAscending = "Leather A-Z"
    case leatherNameDescending = "Leather Z-A"
    case quantityAscending = "Quantity ↑"
    case quantityDescending = "Quantity ↓"
    case purchaseDateAscending = "Purchase Date ↑"
    case purchaseDateDescending = "Purchase Date ↓"
    case lowStockFirst = "Low Stock First"
    case outOfStockFirst = "Out of Stock First"
}

struct StockStatistics {
    let totalEntries: Int
    let lowStockCount: Int
    let outOfStockCount: Int
    let totalValue: Double
    let totalQuantity: Double
    let supplierCounts: [String: Int]
}
