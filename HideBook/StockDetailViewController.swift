//
//  StockDetailViewController.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit
import SnapKit

class StockDetailViewController: UIViewController {
    
    // MARK: - Properties
    private let stockEntry: StockEntry
    
    // MARK: - UI Components
    private let scrollView = UIScrollView()
    private let contentView = UIView()
    private let gradientBackgroundView = GradientView()
    
    // MARK: - Initialization
    init(stockEntry: StockEntry) {
        self.stockEntry = stockEntry
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        populateData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemBackground
        
        // Gradient Background
        gradientBackgroundView.setPrimaryGradient()
        gradientBackgroundView.alpha = 0.05
        
        // Scroll View
        scrollView.showsVerticalScrollIndicator = false
        
        // Content View
        contentView.backgroundColor = .clear
        
        view.addSubviews(gradientBackgroundView, scrollView)
        scrollView.addSubview(contentView)
    }
    
    private func setupConstraints() {
        gradientBackgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Stock Details"
        
        let editButton = UIBarButtonItem(
            barButtonSystemItem: .edit,
            target: self,
            action: #selector(editButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = editButton
    }
    
    private func populateData() {
        // Create info sections
        let sections = [
            createInfoSection(title: "Leather Information", items: [
                ("Leather", stockEntry.leatherName),
                ("Current Stock", stockEntry.formattedQuantity),
                ("Status", stockEntry.stockStatus)
            ]),
            createInfoSection(title: "Purchase Information", items: [
                ("Supplier", stockEntry.supplier.isEmpty ? "Unknown" : stockEntry.supplier),
                ("Batch Number", stockEntry.batchNumber.isEmpty ? "N/A" : stockEntry.batchNumber),
                ("Purchase Date", stockEntry.formattedPurchaseDate),
                ("Unit Price", stockEntry.formattedUnitPrice),
                ("Total Cost", stockEntry.formattedTotalCost)
            ]),
            createInfoSection(title: "Settings", items: [
                ("Low Stock Threshold", String(format: "%.2f %@", stockEntry.lowStockThreshold, stockEntry.areaUnit)),
                ("Notes", stockEntry.notes.isEmpty ? "No notes" : stockEntry.notes)
            ])
        ]
        
        sections.forEach { contentView.addSubview($0) }
        
        // Setup constraints for sections
        for (index, section) in sections.enumerated() {
            section.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
                
                if index == 0 {
                    make.top.equalToSuperview().inset(Constants.Spacing.medium)
                } else {
                    make.top.equalTo(sections[index - 1].snp.bottom).offset(Constants.Spacing.medium)
                }
                
                if index == sections.count - 1 {
                    make.bottom.equalToSuperview().inset(Constants.Spacing.large)
                }
            }
        }
    }
    
    private func createInfoSection(title: String, items: [(String, String)]) -> UIView {
        let sectionView = UIView()
        sectionView.backgroundColor = .appCardBackground
        sectionView.setCornerRadius(Constants.CornerRadius.medium)
        sectionView.applyCardShadow()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = Constants.Fonts.title3
        titleLabel.textColor = .appTextPrimary
        
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = Constants.Spacing.small
        stackView.alignment = .fill
        stackView.distribution = .fill
        
        for (key, value) in items {
            let itemView = createInfoItem(key: key, value: value)
            stackView.addArrangedSubview(itemView)
        }
        
        sectionView.addSubviews(titleLabel, stackView)
        
        titleLabel.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        stackView.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(Constants.Spacing.medium)
            make.leading.trailing.bottom.equalToSuperview().inset(Constants.Spacing.medium)
        }
        
        return sectionView
    }
    
    private func createInfoItem(key: String, value: String) -> UIView {
        let containerView = UIView()
        
        let keyLabel = UILabel()
        keyLabel.text = key
        keyLabel.font = Constants.Fonts.callout
        keyLabel.textColor = .appTextSecondary
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = Constants.Fonts.body
        valueLabel.textColor = .appTextPrimary
        valueLabel.numberOfLines = 0
        valueLabel.textAlignment = .right
        
        containerView.addSubviews(keyLabel, valueLabel)
        
        keyLabel.snp.makeConstraints { make in
            make.leading.top.bottom.equalToSuperview()
            make.width.equalTo(120)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.leading.equalTo(keyLabel.snp.trailing).offset(Constants.Spacing.small)
            make.trailing.top.bottom.equalToSuperview()
        }
        
        containerView.snp.makeConstraints { make in
            make.height.greaterThanOrEqualTo(32)
        }
        
        return containerView
    }
    
    // MARK: - Actions
    @objc private func editButtonTapped() {
        let editVC = AddStockEntryViewController(stockEntry: stockEntry)
        let navController = UINavigationController(rootViewController: editVC)
        present(navController, animated: true)
    }
}
