//
//  LeatherDataManager.swift
//  HideBook
//
//  Created by jj on 2025/5/27.
//

import UIKit

protocol LeatherDataManagerDelegate: AnyObject {
    func dataManagerDidUpdateLeathers(_ manager: <PERSON>therDataManager)
    func dataManager(_ manager: <PERSON><PERSON>DataManager, didFailWithError error: Error)
}

class LeatherDataManager {
    
    // MARK: - Singleton
    static let shared = LeatherDataManager()
    
    // MARK: - Properties
    weak var delegate: LeatherDataManagerDelegate?
    
    private var leathers: [LeatherModel] = []
    private let userDefaults = UserDefaults.standard
    private let leathersKey = "SavedLeathers"
    
    // MARK: - Computed Properties
    var allLeathers: [LeatherModel] {
        return leathers
    }
    
    var favoriteLeathers: [LeatherModel] {
        return leathers.filter { $0.isFavorite }
    }
    
    var leatherCount: Int {
        return leathers.count
    }
    
    // MARK: - Initialization
    private init() {
        loadLeathers()
        
        // 如果没有数据，加载示例数据
        if leathers.isEmpty {
            loadSampleData()
        }
    }
    
    // MARK: - Data Persistence
    private func saveLeathers() {
        do {
            let data = try JSONEncoder().encode(leathers)
            userDefaults.set(data, forKey: leathersKey)
            userDefaults.synchronize()
            
            DispatchQueue.main.async {
                self.delegate?.dataManagerDidUpdateLeathers(self)
            }
        } catch {
            print("Failed to save leathers: \(error)")
            DispatchQueue.main.async {
                self.delegate?.dataManager(self, didFailWithError: error)
            }
        }
    }
    
    private func loadLeathers() {
        guard let data = userDefaults.data(forKey: leathersKey) else {
            return
        }
        
        do {
            leathers = try JSONDecoder().decode([LeatherModel].self, from: data)
        } catch {
            print("Failed to load leathers: \(error)")
            leathers = []
        }
    }
    
    private func loadSampleData() {
        leathers = LeatherModel.sampleData()
        saveLeathers()
    }
    
    // MARK: - CRUD Operations
    func addLeather(_ leather: LeatherModel) {
        leathers.append(leather)
        saveLeathers()
    }
    
    func updateLeather(_ leather: LeatherModel) {
        guard let index = leathers.firstIndex(where: { $0.id == leather.id }) else {
            return
        }
        
        var updatedLeather = leather
        updatedLeather.updateTimestamp()
        leathers[index] = updatedLeather
        saveLeathers()
    }
    
    func deleteLeather(withId id: String) {
        leathers.removeAll { $0.id == id }
        saveLeathers()
    }
    
    func deleteLeather(at index: Int) {
        guard index >= 0 && index < leathers.count else { return }
        leathers.remove(at: index)
        saveLeathers()
    }
    
    func deleteAllLeathers() {
        leathers.removeAll()
        saveLeathers()
    }
    
    func getLeather(withId id: String) -> LeatherModel? {
        return leathers.first { $0.id == id }
    }
    
    func duplicateLeather(withId id: String) -> LeatherModel? {
        guard let originalLeather = getLeather(withId: id) else { return nil }
        
        var duplicatedLeather = originalLeather
        duplicatedLeather = LeatherModel(
            name: "\(originalLeather.name) - 副本",
            materialSource: originalLeather.materialSource,
            tanningMethod: originalLeather.tanningMethod,
            surfaceTreatment: originalLeather.surfaceTreatment,
            thickness: originalLeather.thickness,
            colorCode: originalLeather.colorCode,
            colorDescription: originalLeather.colorDescription,
            origin: originalLeather.origin,
            supplier: originalLeather.supplier,
            brand: originalLeather.brand,
            textureDescription: originalLeather.textureDescription,
            usageSuggestion: originalLeather.usageSuggestion,
            imageName: originalLeather.imageName,
            imageData: originalLeather.imageData,
            tags: originalLeather.tags,
            notes: originalLeather.notes,
            price: originalLeather.price,
            priceUnit: originalLeather.priceUnit,
            isFavorite: false
        )
        
        addLeather(duplicatedLeather)
        return duplicatedLeather
    }
    
    // MARK: - Search & Filter
    func searchLeathers(with searchText: String) -> [LeatherModel] {
        guard !searchText.isEmpty else { return leathers }
        return leathers.filter { $0.matchesSearchText(searchText) }
    }
    
    func filterLeathers(by materialSource: String? = nil,
                       tanningMethod: String? = nil,
                       surfaceTreatment: String? = nil,
                       textureDescription: String? = nil,
                       isFavorite: Bool? = nil) -> [LeatherModel] {
        
        return leathers.filter { leather in
            if let materialSource = materialSource, leather.materialSource != materialSource {
                return false
            }
            if let tanningMethod = tanningMethod, leather.tanningMethod != tanningMethod {
                return false
            }
            if let surfaceTreatment = surfaceTreatment, leather.surfaceTreatment != surfaceTreatment {
                return false
            }
            if let textureDescription = textureDescription, leather.textureDescription != textureDescription {
                return false
            }
            if let isFavorite = isFavorite, leather.isFavorite != isFavorite {
                return false
            }
            return true
        }
    }
    
    func getLeathersByTag(_ tag: String) -> [LeatherModel] {
        return leathers.filter { $0.hasTag(tag) }
    }
    
    func getAllTags() -> [String] {
        let allTags = leathers.flatMap { $0.tags }
        return Array(Set(allTags)).sorted()
    }
    
    // MARK: - Sorting
    func sortedLeathers(by sortType: LeatherSortType) -> [LeatherModel] {
        switch sortType {
        case .nameAscending:
            return leathers.sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedAscending }
        case .nameDescending:
            return leathers.sorted { $0.name.localizedCaseInsensitiveCompare($1.name) == .orderedDescending }
        case .createdDateAscending:
            return leathers.sorted { $0.createdAt < $1.createdAt }
        case .createdDateDescending:
            return leathers.sorted { $0.createdAt > $1.createdAt }
        case .updatedDateAscending:
            return leathers.sorted { $0.updatedAt < $1.updatedAt }
        case .updatedDateDescending:
            return leathers.sorted { $0.updatedAt > $1.updatedAt }
        case .thicknessAscending:
            return leathers.sorted { $0.thickness < $1.thickness }
        case .thicknessDescending:
            return leathers.sorted { $0.thickness > $1.thickness }
        case .priceAscending:
            return leathers.sorted { ($0.price ?? 0) < ($1.price ?? 0) }
        case .priceDescending:
            return leathers.sorted { ($0.price ?? 0) > ($1.price ?? 0) }
        case .favoriteFirst:
            return leathers.sorted { $0.isFavorite && !$1.isFavorite }
        }
    }
    
    // MARK: - Statistics
    func getStatistics() -> LeatherStatistics {
        let totalCount = leathers.count
        let favoriteCount = favoriteLeathers.count
        let materialSourceCounts = Dictionary(grouping: leathers, by: { $0.materialSource })
            .mapValues { $0.count }
        let tanningMethodCounts = Dictionary(grouping: leathers, by: { $0.tanningMethod })
            .mapValues { $0.count }
        
        let averageThickness = leathers.isEmpty ? 0 : leathers.reduce(0) { $0 + $1.thickness } / Double(leathers.count)
        
        return LeatherStatistics(
            totalCount: totalCount,
            favoriteCount: favoriteCount,
            materialSourceCounts: materialSourceCounts,
            tanningMethodCounts: tanningMethodCounts,
            averageThickness: averageThickness
        )
    }
}

// MARK: - Supporting Types
enum LeatherSortType: String, CaseIterable {
    case nameAscending = "名称 A-Z"
    case nameDescending = "名称 Z-A"
    case createdDateAscending = "创建时间 ↑"
    case createdDateDescending = "创建时间 ↓"
    case updatedDateAscending = "更新时间 ↑"
    case updatedDateDescending = "更新时间 ↓"
    case thicknessAscending = "厚度 ↑"
    case thicknessDescending = "厚度 ↓"
    case priceAscending = "价格 ↑"
    case priceDescending = "价格 ↓"
    case favoriteFirst = "收藏优先"
}

struct LeatherStatistics {
    let totalCount: Int
    let favoriteCount: Int
    let materialSourceCounts: [String: Int]
    let tanningMethodCounts: [String: Int]
    let averageThickness: Double
}
